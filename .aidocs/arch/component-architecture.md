# Unified Mobile App Architecture

## Environment Configuration

### API Environments

The app supports multiple API environments:

- **Production API**: `https://api.prod.unified.community/v1` (default)
- **Development API**: `https://api.dev.unified.community/v1`
- **Local API**: Custom URL that can be set for local development

The current environment is stored in `AsyncStorage` with the key `APIURL`.

### Environment Detection

To check the current environment:

```javascript
const isDev = async () => {
  const host = await AsyncStorage.getItem('APIURL')
  if (host) {
    return host.includes('api.dev.unified')
  }
  return false
}
```

The app also uses `__DEV__` global variable for React Native development mode detection.

### Environment Switching

Users can switch environments through the DevMenu component, accessed by tapping the "Create account" text 5 times in sequence on the main screen.

The `toggleEnvironment()` function in `components/Main/index.js` handles switching between environments:

```javascript
const toggleEnvironment = async newHost => {
  await globalActions.resetAuth()
  if (newHost.dev) {
    await AsyncStorage.setItem('APIURL', 'https://api.dev.unified.community/v1')
  } else if (newHost.local) {
    await AsyncStorage.setItem('APIURL', local)
  } else {
    await AsyncStorage.setItem('APIURL', 'https://api.prod.unified.community/v1')
  }
  await globalActions.resetAPI()
}
```

## URL Structure

### Domain Configuration

The app uses different domains for different environments:

- **Production**:
  - `unified.me` - Main domain
  - `joinunified.us` - Legacy/alternate domain

- **Development**:
  - `dev.unified.me` - Development domain

### Content URL Patterns

Shareable URLs follow these patterns:

- **Posts**: `https://unified.me/post/{postId}`
- **Profiles**: `https://unified.me/profile/{profileId}`
- **Organizations**: `https://unified.me/organization/{orgId}`
- **Chat invites**: `https://unified.me/chat-invite/{inviteId}`
- **Invite links**: `https://www.joinunified.us/invite?code={inviteCode}`
- **Account confirmation**: `https://unified.me/confirm-account/{confirmationId}`
- **Password reset**: `https://unified.me/reset_password/{token}`

When in development environment, the domain changes to `dev.unified.me`.

## Deep Linking

### Deep Link Configuration

- **Custom URL scheme**: `unifiedcommunity://`
- **Universal links (iOS)**: `unified.me` and `dev.unified.me`
- **App Links (Android)**: Same domains

### Deep Link Processing

1. In `navigation/deepLinks.js`, the `processUrlWithSession()` function parses incoming URLs and processes them based on the path structure.

2. The app handles various deep link formats:
   - `https://unified.me/profile/{id}` → Opens the profile screen
   - `https://unified.me/post/{id}` → Opens the post screen
   - `https://unified.me/organization/{id}` → Opens the organization screen
   - `https://unified.me/chat-invite/{id}` → Opens the chat room

3. The URL parsing extracts the resource ID and type, then uses the `processNotification()` function to navigate to the appropriate screen.

### Sharing Implementation

Profile sharing (and other content sharing) uses the device's native share functionality:

```javascript
const onShare = async () => {
  try {
    // Check environment
    const isDevEnv = await isDev()
    
    // Generate appropriate URL
    let message = `https://unified.me/profile/${profile.account?.id}`
    if (isDevEnv) {
      message = `https://dev.unified.me/profile/${profile.account?.id}`
    }
    
    // Use native sharing
    await Share.share({ message })
  } catch (error) {
    console.log(error.message)
  }
}
```

## Global State Management

The app uses a custom state management system with the following components:

- `contexts/store.js` - Main global state store
- `useGlobalState()` hook - Provides access to the global state and actions
- Component-specific local stores (e.g., `components/Profile/LocalStore/index.js`)

The API client is initialized in the global state and automatically uses the correct environment based on the stored API URL.

## Navigation System

The app uses React Native Navigation (Wix) for screen management with the following structure:

- **Navigation Registration**: All screens are registered in `navigation/registerScreens.js`
- **Navigation Flow**: Main navigation flow defined in `navigation/index.js`
- **Deep Link Handling**: Deep links processed in `navigation/deepLinks.js`
- **Tab-Based Navigation**: Main app uses bottom tabs for primary navigation
- **Stack Navigation**: Screens are pushed/popped in stack-based navigation model

Navigation functions use a consistent pattern:

```javascript
// Example navigation function
export const goProfile = (componentId, props = {}) => {
  Navigation.push(componentId, {
    component: {
      name: 'Profile',
      passProps: props,
      options: { /* screen options */ }
    }
  })
}
```

## Component Structure

The app follows a feature-based component organization:

- **Component Directory Structure**:
  - Main feature folders (`Profile`, `Feed`, `Chat`, etc.)
  - Component-specific subfolders (e.g., `Header`, `Banner`, `Options`)
  - Common utilities and shared components

- **Component Organization**:
  - `index.js` - Component implementation
  - `styles.js` - StyleSheetFactory for light/dark theming
  - `navigation.js` - Screen-specific navigation functions
  - `LocalStore/` - Component-specific state management

## API Integration

The API client architecture is modular and resource-oriented:

- **Main API Client**: `api/Unified/index.js` initializes the Axios instance and loads all API modules
- **Resource Modules**: Separate modules for each API resource (users, posts, chat, etc.)
- **Core API Methods**: Common HTTP methods abstracted in `api/Unified/core.js`

API initialization and token management:

```javascript
// API initialization with token
async init(authToken) {
  this._authToken = authToken || null
  const host = await this.getHost()
  
  const axiosOpts = {
    baseURL: host ? host : API_HOST,
    headers: {},
  }
  
  if (this._authToken) {
    axiosOpts.headers = {
      Authorization: `Bearer ${this._authToken}`,
    }
  }
  
  this.http = axios.create(axiosOpts)
  
  // Initialize all API resource modules
  this.users = new UsersAPI(this.http)
  this.posts = new PostsAPI(this.http)
  // More modules...
}
```

## Authentication Flow

The app uses token-based authentication with these key components:

- **Login**: Email/password or phone authentication via `/auth/login` endpoint
- **Registration**: Multi-step registration flow with verification
- **Token Storage**: JWT stored securely in AsyncStorage/MMKV
- **Token Refresh**: Automatic token refresh when expired
- **Session Management**: Session tracking with unique IDs

## UI Theming

The app supports light and dark themes with:

- **StyleSheetFactory Pattern**: Each component uses a factory pattern for theme-specific styles
- **Dynamic Theming**: `useColorScheme()` hook detects system theme preference
- **Centralized Colors**: Common colors defined in `components/Utils/colors.js`
- **Conditional Rendering**: Components render differently based on theme

```javascript
// Example StyleSheetFactory
const StyleSheetFactory = {
  getSheet(isDark) {
    return StyleSheet.create({
      container: {
        backgroundColor: isDark ? '#121212' : 'white',
      },
      text: {
        color: isDark ? 'white' : 'black',
      },
      // More styles...
    })
  }
}
```

## Data Persistence

The app uses multiple persistence strategies:

- **AsyncStorage**: For legacy storage and non-performance-critical data
- **MMKV**: High-performance storage for frequently accessed data
- **In-Memory Caching**: Temporary caching for optimized rendering
- **Custom Hooks**: `useMMKVObject` and similar hooks for easy storage access

## Key Features Implementation

- **Profile System**: User profiles with follow/block capabilities
- **Feed**: Social content feed with customizable filters
- **Chat**: Real-time messaging with group and direct messages
- **Organizations**: Group membership and management
- **Actions**: Social action campaigns like voter registration
- **Notifications**: Push and in-app notification system

## Dependencies and Libraries

Key libraries and dependencies:

- **React Native**: Core framework (v0.76.9)
- **React Native Navigation**: Screen management
- **React Sweet State**: State management
- **Axios**: API client
- **OneSignal**: Push notifications
- **MMKV/AsyncStorage**: Data persistence
- **FontAwesome**: Icon system
- **Day.js**: Date manipulation
- **React Native Fast Image**: Optimized image rendering