# Toast Notification System

## Overview
The Toast system in the Unified mobile app provides a consistent way to show user feedback notifications. It uses React Context and Action Sheets to display stacked messages with different types (success/notice or error).

## Architecture

### Component Structure
```
ToastStackWrapper (Context Provider)
├── ToastContext
├── Toast Component(s)
├── ActionSheet (Container)
└── ConfirmAlert (Optional)
```

### Key Components

#### 1. ToastStackWrapper (`/components/Home/ToastStackWrapper/index.js`)
- Context provider that wraps the app
- Manages toast state and visibility
- Provides `setToasts`, `toastRef`, and `showConfirmActionSheet` via context

#### 2. Toast Component (`/components/Toast/index.js`)
- Individual toast UI component
- Handles message display, type styling, and animations
- Props: `message`, `type`, `onClose`, `duration`, `id`

#### 3. ToastContext
- React Context for sharing toast functionality across components
- Accessible via `useContext(ToastContext)`

## Usage Guide

### Basic Setup
```javascript
import { useContext } from 'react'
import { ToastContext } from '../../Home/ToastStackWrapper'

const YourComponent = () => {
  const { setToasts, toastRef } = useContext(ToastContext)
  
  // ... component logic
}
```

### Showing a Toast

#### Success/Notice Toast
```javascript
const showSuccessToast = (message) => {
  toastRef?.current.hide() // Hide any existing toasts
  setToasts(prevToasts => [...prevToasts, { 
    id: Date.now(), 
    message: message,
    type: 'notice'
  }])
  toastRef.current.show()
}
```

#### Error Toast
```javascript
const showErrorToast = (message) => {
  toastRef?.current.hide()
  setToasts(prevToasts => [...prevToasts, { 
    id: Date.now(), 
    message: message,
    type: 'error'
  }])
  toastRef.current.show()
}
```

### Toast Types
- `notice`: Default style, shows in purple (dark mode) or black (light mode)
- `error`: Shows in pink color (`unifiedErrorPink`)

### Toast Features
- Multiple toasts can stack
- Each toast has an "OK" button to dismiss
- Toasts can be swiped away
- Animation on entry (slides up)
- Auto-clearing when action sheet closes

## Common Patterns

### API Error Handling
```javascript
try {
  const result = await api.someCall()
  // Success
  const message = 'Operation successful!'
  setToasts(prevToasts => [...prevToasts, { 
    id: Date.now(), 
    message, 
    type: 'notice' 
  }])
  toastRef.current.show()
} catch (e) {
  // Error
  const message = e?.info?.responseBody?.message || 'An error occurred'
  setToasts(prevToasts => [...prevToasts, { 
    id: Date.now(), 
    message, 
    type: 'error' 
  }])
  toastRef.current.show()
}
```

### Form Validation
```javascript
if (!isValidInput) {
  const message = 'Please check your input'
  setToasts(prevToasts => [...prevToasts, { 
    id: Date.now(), 
    message, 
    type: 'error' 
  }])
  toastRef.current.show()
  return
}
```

## Styling Details

### Toast Container
- Full width minus 20px margin
- Rounded corners (borderRadius: 16)
- Padding: 24px horizontal, 24px vertical
- Flex row layout with space between

### Message Text
- Font: calibri, 14px, medium weight
- Color: white
- Flex: 12 (takes most space)

### OK Button
- Font: calibri, 14px, medium weight
- Color: white
- Positioned at flex-end

### Background Colors
- Notice: `unifiedNoticePurple` (dark mode) or black (light mode)
- Error: `unifiedErrorPink`

## Best Practices

1. **Always hide existing toasts** before showing new ones to prevent overlap
2. **Use meaningful messages** that help users understand what happened
3. **Choose appropriate type** - use 'error' for failures, 'notice' for success/info
4. **Handle API errors gracefully** - extract message from error response or provide fallback
5. **Keep messages concise** - toasts are for quick feedback, not detailed explanations
6. **Unique IDs** - use `Date.now()` for toast IDs to ensure uniqueness

## Example Implementation
```javascript
const ProfileComponent = () => {
  const { setToasts, toastRef } = useContext(ToastContext)
  
  const inviteToOrganizations = async () => {
    try {
      await api.orgs.createInvite(orgId, userId)
      
      // Show success toast
      setToasts(prevToasts => [...prevToasts, { 
        id: Date.now(), 
        message: 'Invite(s) sent!',
        type: 'notice'
      }])
      toastRef.current.show()
    } catch (error) {
      // Show error toast
      const message = error?.info?.responseBody?.message || 
                     'Failed to send invite'
      setToasts(prevToasts => [...prevToasts, { 
        id: Date.now(), 
        message,
        type: 'error'
      }])
      toastRef.current.show()
    }
  }
}
```