# Unified Mobile App Architecture Documentation

## Introduction

This documentation provides a comprehensive overview of the Unified Mobile app's architecture, design patterns, and implementation details. The Unified app is a React Native application built for both iOS and Android platforms, featuring social networking, chat, and civic engagement capabilities.

```mermaid
graph TD
    A[Unified Mobile App] --> B[React Native Core]
    B --> B1[iOS Implementation]
    B --> B2[Android Implementation]
    
    A --> C[Architecture Layers]
    C --> C1[UI Components]
    C --> C2[Navigation]
    C --> C3[State Management]
    C --> C4[API Client]
    C --> C5[Persistence]
    
    A --> D[Key Features]
    D --> D1[Social Platform]
    D --> D2[Chat System]
    D --> D3[Civic Engagement Tools]
    D --> D4[Organizations]
    D --> D5[Deep Linking]
```

## Documentation Sections

1. [Architecture Overview](architecture-overview.md) - High-level overview of the app's architecture
2. [State Management](state-management.md) - How state is managed throughout the application
3. [API and Networking](api-networking.md) - API client structure and networking patterns
4. [Navigation](navigation.md) - Navigation system and screen management
5. [Component Structure](component-structure.md) - Component organization and implementation patterns

## Key Technical Aspects

### Framework and Libraries

- **React Native**: Core framework for cross-platform development
- **React Native Navigation (Wix)**: Native navigation implementation
- **React Sweet State**: State management library
- **Axios**: HTTP client for API requests
- **MMKV/AsyncStorage**: Data persistence solutions

```mermaid
graph LR
    A[Core Dependencies] --> B[React Native 0.76.9]
    A --> C[React 18.3.1]
    A --> D[React Native Navigation 7.45.0]
    A --> E[React Sweet State 2.5.2]
    
    F[Data Management] --> G[Axios 0.21.1]
    F --> H[MMKV 2.12.2]
    F --> I[AsyncStorage 1.15.7]
    
    J[UI Elements] --> K[React Native Gesture Handler 2.12.0]
    J --> L[React Native Modal 11.10.0]
    J --> M[FontAwesome Icons]
    
    N[Key Integrations] --> O[OneSignal 5.2.9]
    N --> P[React Native Contacts 7.0.3]
    N --> Q[React Native Device Info 8.7.1]
```

### Architectural Patterns

- **Feature-based Structure**: Components organized by feature rather than type
- **Multi-layer State Management**: Combination of global state, component state, and persistence
- **Component Composition**: Building screens from smaller, reusable components
- **Domain-specific API Modules**: API functionality organized into domain-specific modules

```mermaid
flowchart TD
    subgraph "Feature-based Structure"
        A1[Profile Feature]
        A2[Chat Feature]
        A3[Feed Feature]
    end
    
    subgraph "State Management Layers"
        B1[Global State]
        B2[Component State]
        B3[Persistent Storage]
    end
    
    subgraph "Component Composition"
        C1[Screen Components]
        C2[Feature Components]
        C3[UI Components]
    end
    
    subgraph "API Organization"
        D1[API Client]
        D2[Users Module]
        D3[Chat Module]
        D4[Posts Module]
    end
    
    A1 --- A2
    A2 --- A3
    
    B1 --- B2
    B2 --- B3
    
    C1 --- C2
    C2 --- C3
    
    D1 --- D2
    D1 --- D3
    D1 --- D4
```

### Key Features

- **Authentication**: Comprehensive login, registration, and profile management
- **Social Networking**: User profiles, posts, comments, likes, follows
- **Chat System**: Direct messages, group chats, organization channels
- **Civic Engagement**: Contact representatives, voter registration tools
- **Organizations**: Create and manage organizations, member management
- **Deep Linking**: Handle various external link types
- **Push Notifications**: OneSignal integration for push notifications

## Design Philosophy

The application follows several key design principles:

1. **Component Isolation**: Components focus on specific functionality
2. **State Separation**: Different types of state managed appropriately
3. **Responsive Design**: UI adapts to different screen sizes and orientations
4. **Theme Support**: Light and dark mode implementation
5. **Performance Optimization**: Techniques to ensure smooth performance

```mermaid
mindmap
  root((Design Philosophy))
    Component Design
      Isolation
      Reusability
      Composition
    State Management
      Global vs. Local
      Persistence Strategy
      Reactivity
    UI Principles
      Responsive Design
      Theme Support
      Consistent Styling
    Performance
      Optimization Techniques
      Rendering Efficiency
      API Request Management
```

## Environment Configuration

The app supports multiple environments:

- **Production**: `https://api.prod.unified.community/v1`
- **Development**: `https://api.dev.unified.community/v1`

Environment-specific domains for deep linking:
- Production: `unified.me`
- Development: `dev.unified.me`

## Development Practices

As documented in the [CLAUDE.md](../../CLAUDE.md) file, the project follows these practices:

- **Code Style**: 2-space indentation, single quotes, no semicolons
- **Component Naming**: PascalCase for components, camelCase for functions/variables
- **File Structure**: Component in index.js, styles in styles.js
- **React Patterns**: Functional components with hooks
- **Styling**: StyleSheetFactory for theme support

## Integration Points

```mermaid
graph TD
    A[App Integration Points] --> B[OneSignal Notifications]
    A --> C[Device Contacts]
    A --> D[Deep Links]
    A --> E[Media Capture]
    A --> F[Environment Config]
    
    B --> B1[Push Notifications]
    B --> B2[In-app Messages]
    
    C --> C1[Contact Import]
    C --> C2[Contact Actions]
    
    D --> D1[URL Handling]
    D --> D2[Notification Routing]
    
    E --> E1[Camera Integration]
    E --> E2[Gallery Access]
    
    F --> F1[Development/Production]
    F --> F2[API Endpoints]
```

## Additional Resources

- **Build Commands**: See [CLAUDE.md](../../CLAUDE.md) for build and test commands
- **Environment Setup**: Instructions for configuring development environments
- **Deep Linking**: Documentation of supported deep link formats

---

This architecture documentation is designed to help developers understand the structure and patterns used throughout the Unified Mobile app, making it easier to maintain, extend, and improve the codebase.