# State Management in Unified Mobile App

## Overview
The Unified Mobile app uses a multi-layered approach to state management, combining global application state, component-specific state, and persistent storage strategies. This document outlines how state is managed throughout the application.

```mermaid
graph TD
    A[State Management] --> B[Global State]
    A --> C[Component State]
    A --> D[Persistent Storage]
    
    B --> B1[React Sweet State]
    B1 --> B1a[Authentication]
    B1 --> B1b[User Profile]
    B1 --> B1c[API Client]
    
    C --> C1[React useState]
    C --> C2[Component Local Stores]
    C --> C3[Context API]
    
    D --> D1[MMKV]
    D --> D2[AsyncStorage]
    
    D1 --> D1a[Fast, In-memory Storage]
    D2 --> D2a[Persistent, Larger Objects]
    
    E[State Flow] --> E1[UI Events]
    E --> E2[API Responses]
    E --> E3[Deep Links]
    E --> E4[Push Notifications]
    
    E1 --> B
    E2 --> B
    E2 --> C
    E2 --> D
    E3 --> B
    E4 --> B
```

## Key Technologies

### React Sweet State
The primary state management library is React Sweet State, which provides a Redux-like pattern with less boilerplate:

- Defined in `contexts/store.js`
- Provides global state and actions
- Accessed through hooks in components

### Local Component State
- React's built-in `useState` for component-specific state
- Custom hooks for reusable state logic
- Component-specific state stores in `LocalStore` directories

### Persistent Storage
- `react-native-mmkv`: Fast, encrypted key-value storage
- `@react-native-async-storage/async-storage`: Persistent storage for larger objects
- Custom hooks like `useMMKVObject` for accessing stored data

## Global State Structure

The main global state store (`contexts/store.js`) contains:

```javascript
initialState: {
  syncURL: 'https://www.joinunified.us/features/sync-contacts',
  privacyURL: 'https://www.joinunified.us/privacy-policy',
  authToken: '',
  api: new UAPI(''),
  profile: {},
  mmkv: new MMKV(),
  lastAction: null,
  session_id: null,
  first_launch: true,
}
```

```mermaid
classDiagram
    class GlobalState {
        +string syncURL
        +string privacyURL
        +string authToken
        +UAPI api
        +Object profile
        +MMKV mmkv
        +Object lastAction
        +string session_id
        +boolean first_launch
    }
    
    class GlobalActions {
        +setLastAction()
        +setProfile()
        +resetAPI()
        +setAuth()
        +setMMKV()
        +resetAuth()
        +logEvent()
        +setSessionId()
        +setFirstLaunch()
    }
    
    GlobalState <-- GlobalActions
```

Key global state components:

1. **Authentication**: Token and session management
2. **API Client**: Initialized API client instance
3. **User Profile**: Current user's profile data
4. **Storage**: References to storage instances
5. **Application State**: Flags like first_launch

## Global Actions

The global store defines several actions that can be dispatched:

- `setLastAction`: Track the last action performed
- `setProfile`: Update the user profile
- `resetAPI`: Reset the API client with no authentication
- `setAuth`: Set authentication token and initialize API client
- `setMMKV`: Update the MMKV storage instance
- `resetAuth`: Clear authentication and related data
- `logEvent`: Log events to the backend
- `setSessionId`: Generate and set a session ID
- `setFirstLaunch`: Update the first launch flag

## Component-Level State

Components often have their own state stores for feature-specific data:

### Example: Profile Component
- `components/Profile/LocalStore/index.js` defines a state store specific to the Profile feature
- Uses React Sweet State to create a scoped store
- Contains profile-specific state and actions

```javascript
// Example pattern found in component state stores
const Store = createStore({
  initialState: {
    // Component-specific state...
    reloadKey: 0,
  },
  actions: {
    // Component-specific actions...
    setReloadKey: () => ({setState}) => {
      setState({reloadKey: Date.now()})
    }
  },
  name: 'profileState',
})
```

```mermaid
graph TD
    A[Component State] --> B[Feature-specific Stores]
    A --> C[Local UI State]
    A --> D[Custom Hooks]
    
    B --> B1[Profile Store]
    B --> B2[Chat Store]
    B --> B3[Feed Store]
    
    B1 --> B1a[reloadKey]
    B1 --> B1b[Feature-specific Actions]
    
    C --> C1[useState]
    C --> C2[useRef]
    
    D --> D1[useProfileState]
    D --> D2[useChatState]
    D --> D3[useMMKVObject]
```

## State Persistence Strategy

The app uses a tiered approach to persistence:

1. **In-Memory State**: React state and Sweet State for runtime data
2. **MMKV**: For frequently accessed smaller data that needs to persist
3. **AsyncStorage**: For larger objects and less frequently accessed data

```mermaid
flowchart TD
    A[Data to Store] --> B{Critical?}
    B -->|Yes| C{Size?}
    B -->|No| D[React State]
    
    C -->|Small| E[MMKV]
    C -->|Large| F[AsyncStorage]
    
    E --> G[Retrieve on App Start]
    F --> G
    
    G --> H[Global State]
    
    subgraph "Persistence Strategy"
        D
        E
        F
    end
    
    subgraph "Access Patterns"
        G
        H
    end
```

### Persistence Examples

- User Profile:
  ```javascript
  // Store profile in MMKV
  globalState.mmkv.set('PROFILE', JSON.stringify(response))
  
  // Retrieve profile from MMKV
  savedProfile = globalState.mmkv.getString('PROFILE')
  savedProfile = JSON.parse(savedProfile)
  ```

- Authentication Token:
  ```javascript
  // Store token in AsyncStorage
  AsyncStorage.setItem(TOKEN, token)
  
  // Retrieve token
  const token = await AsyncStorage.getItem(TOKEN)
  ```

## State Flow Patterns

```mermaid
sequenceDiagram
    participant User
    participant Component
    participant LocalStore
    participant GlobalStore
    participant PersistentStorage
    participant API
    
    User->>Component: Interaction
    Component->>LocalStore: Update local state
    Component->>GlobalStore: Update global state (if needed)
    GlobalStore->>PersistentStorage: Persist critical data
    Component->>API: Make API request
    API->>Component: Response
    Component->>LocalStore: Update with response data
    Component->>GlobalStore: Update with response data
    GlobalStore->>PersistentStorage: Persist updated data
    Component->>User: Update UI
```

### Authentication Flow
1. User logs in → API returns token
2. Token stored in AsyncStorage and added to global state
3. API client reinitialized with new token
4. Profile data fetched and stored in both global state and MMKV

### Profile Data Flow
1. App loads → Check for cached profile in MMKV
2. Display cached profile while fetching fresh data
3. Update both global state and MMKV with fresh profile data
4. Components react to global state updates

### Component Data Loading
1. Component mounts → Check if data needed exists in global state
2. If data missing, check component's local storage
3. If still missing, fetch from API
4. Update all relevant state stores

## Optimizations

### Caching
- Profile data cached in MMKV for quick access
- Session-specific information persisted between app launches

### Debouncing
- UI interactions debounced to prevent excessive state updates
- Example: Scroll events in Profile component use lodash's `debounce`

### State Reuse
- Global state used for cross-component data
- Component-specific state used for isolated functionality

## Environment-Aware State

The app adapts its state based on the environment:

```javascript
// Get environment-specific API URL
async getHost() {
  const host = await AsyncStorage.getItem('APIURL')
  return host
}
```

## Reactive Data Patterns

The app employs several reactive patterns:

1. **Watchers**: Using `useEffect` to react to state changes
2. **Conditional Fetching**: Fetching data based on state conditions
3. **Cascade Updates**: Changes in one state triggering updates in dependent state

```mermaid
graph TD
    A[State Change] --> B{Type of Change}
    
    B -->|Auth Token| C[Reinitialize API Client]
    C --> D[Fetch User Profile]
    D --> E[Update Profile State]
    E --> F[Update UI]
    
    B -->|Profile Data| G[Update Global Profile]
    G --> H[Store in MMKV]
    G --> I[Update UI Components]
    
    B -->|UI State| J[Component Re-render]
    
    B -->|Environment| K[Update API Endpoints]
    K --> L[Reset Required Data]
```

## State and Navigation Integration

- Screen state preserved in navigation stack
- Deep link handling influenced by state
- Navigation actions often trigger state updates

## Best Practices Implemented

1. **Separation of Concerns**: State logic separated from UI components
2. **Persistent Storage Strategy**: Appropriate storage methods for different data types
3. **Optimized Updates**: Debouncing and selective updates to prevent performance issues
4. **Error Handling**: Retry mechanisms for failed state updates
5. **State Hydration**: Loading cached state before fresh data fetches