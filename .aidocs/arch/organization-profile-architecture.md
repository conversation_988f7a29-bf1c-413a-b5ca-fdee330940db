# Organization Profile Architecture

This document outlines the architecture of the organization profile components in the Unified mobile app, focusing on their structure, state management, and UI/UX patterns.

## Component Structure

The organization profile implementation follows a pattern similar to the user profile but with organization-specific features and permissions. The main components are:

1. **OrgProfile** (`components/OrgProfile/index.js`)
   - Root component that manages the overall state and layout
   - <PERSON>les animation logic for scrolling effects
   - Coordinates between child components

2. **Banner** (`components/OrgProfile/Banner/index.js`)
   - Displays the organization's banner image
   - Contains header action buttons (back, share, settings)
   - Adapts its appearance based on scroll position

3. **Header** (`components/OrgProfile/Header/index.js`)
   - Shows organization bio and metadata
   - Displays action buttons for community count
   - Contains follow/chat buttons

4. **TabBar** (`components/OrgProfile/TabBar/index.js`)
   - Navigation between different sections (Posts, Actions, Impact, Members, Info)

5. **Options & MainUserOptions** (`components/OrgProfile/Options/index.js` & `components/OrgProfile/MainUserOptions/index.js`)
   - Context menus with different actions based on the user's relationship to the organization

## Role-Based Permission System

The organization profile implements a role-based permission system that determines what actions a user can take:

1. **Non-Members** (profile.role === null)
   - Can view organization profile
   - Can request to join the organization
   - Can report the organization
   - Can share the organization profile

2. **Members** (profile.role !== null && profile.role !== 'owner')
   - Can leave the organization
   - Can report the organization
   - Can share the organization profile

3. **Owners/Admins** (profile.role === 'owner')
   - Can manage organization settings
   - Can access organization management
   - Cannot leave without transferring ownership
   - Can share the organization profile

This role-based system ties into the backend scopes system, which includes permissions like:
```
"scopes": ["leave", "edit_settings_visibility", "invite", "view_profile", "remove_member", "chat_delete_rooms", ...]
```

## UI Design Patterns

The organization profile implements several UI patterns:

1. **Animated Headers**
   - Banner image blurs and shrinks as the user scrolls
   - Profile picture moves and resizes

2. **Context Menus**
   - Action sheet-based menus with role-specific options
   - Different styles for regular options vs. warning/destructive actions

3. **Environmental Awareness**
   - Adapts URLs for different environments (dev vs. production)
   - Uses AsyncStorage to detect current environment

## State Management

The OrgProfile component uses several state sources:

1. **Local Component State** 
   - Manages UI state like animations, selected tabs, etc.

2. **OrgProfileState** (`components/OrgProfile/LocalStore/index.js`)
   - Manages organization-specific data
   - Handles loading states and reload triggers

3. **Global State**
   - Accesses app-wide state like authentication, theme, etc.
   - Records analytics events (e.g., when sharing profile)

## Integration with Chat

The organization profile integrates with the chat system:

1. **General Chat Room**
   - Organizations have a "General" chat room
   - The organization profile loads this room ID to enable direct navigation
   - This is managed through the `chatGeneralRoomId` state 

## API Integration

The organization profile interacts with several API endpoints:

1. **Organization Data** - `globalState.api.orgs.getOrg(orgId)`
2. **Membership Management**:
   - Join: `globalState.api.orgs.requestToJoin(account.id)`
   - Leave: `globalState.api.orgs.leaveOrg(orgId)`
3. **Reporting**: Uses the ReportDialog component

## Lessons & Design Patterns

1. **Component Mirroring**
   - The OrgProfile architecture mirrors the Profile architecture but with organization-specific logic
   - This allows for code reuse and consistent UX between different profile types

2. **Role-Based UI**
   - UI elements and actions are conditionally rendered based on user role
   - This creates a tailored experience for different user types

3. **Decoupled State Management**
   - Each component family (Profile, OrgProfile) has its own state management
   - This prevents cross-contamination between different contexts

4. **Environment-Aware Sharing**
   - All sharing functions check the current environment
   - This ensures correct URLs are shared based on the app's running environment

These patterns provide a flexible, maintainable architecture for managing organization profiles with different user roles and permissions.