# Navigation Architecture

## Overview
The Unified Mobile app uses React Native Navigation (from Wix) rather than the more common React Navigation library. This implementation provides native navigation with optimized performance and a component-based approach to screen management.

```mermaid
graph TD
    A[Navigation Architecture] --> B[Navigation Setup]
    A --> C[Screen Registration]
    A --> D[Navigation Patterns]
    A --> E[Deep Linking]
    
    B --> B1[React Native Navigation]
    B --> B2[Root Configuration]
    B --> B3[Navigation Options]
    
    C --> C1[Component Registration]
    C --> C2[Wrapper HOCs]
    C --> C3[Screen Definitions]
    
    D --> D1[Tab Navigation]
    D --> D2[Stack Navigation]
    D --> D3[Modal Presentation]
    
    E --> E1[URL Handling]
    E --> E2[Route Mapping]
    E --> E3[Navigation Actions]
```

## Core Navigation Structure

### Main Navigation Components

The navigation system is established in several key files:

1. `navigation/index.js`: Defines navigation functions for primary app flows
2. `navigation/registerScreens.js`: Registers all screens with the Navigation library
3. `navigation/deepLinks.js`: Handles deep linking for external URLs
4. `index.js` (root): Sets up initial navigation and app launch behavior

```mermaid
classDiagram
    class NavigationSystem {
        +registerScreens()
        +goMain()
        +goHome()
        +goCreateProfile()
        +goResetPassword()
        +setupDefaultOptions()
    }
    
    class DeepLinkHandling {
        +startDeepLinkEvent()
        +startDeepLinkBackgroundListener()
        +processUrl()
        +processUrlWithSession()
    }
    
    class AppInitialization {
        +checkAuthToken()
        +setupInitialRoot()
    }
    
    NavigationSystem <-- AppInitialization
    DeepLinkHandling <-- AppInitialization
```

### Screen Registration

All screens are registered using the Navigation library's registration pattern:

```javascript
// In registerScreens.js
Navigation.registerComponent('Profile', () => wrapWithWrapper(require('../components/Profile').default))
```

The app uses a custom wrapper function to provide consistent functionality across all screens:

```javascript
const wrapWithWrapper = Component =>
  gestureHandlerRootHOC(props => (
    <ToastStackWrapper>
      <Component {...props} />
    </ToastStackWrapper>
  ))
```

This wrapper provides:
1. Gesture handler integration
2. Toast notification support
3. Consistent context providers

```mermaid
graph LR
    A[Screen Component] --> B[Gesture Handler HOC]
    B --> C[Toast Wrapper]
    C --> D[Registered Component]
    
    E[Navigation.registerComponent] --> F[Component Name]
    E --> D
    F --> G[Navigation Registry]
    D --> G
```

## Navigation Types

### Tab-Based Navigation

The main app interface uses a bottom tab navigator defined in `goHome()`:

```javascript
export const goHome = () => {
  const config = {
    root: {
      bottomTabs: {
        id: 'HomeTabs',
        // Configuration...
        children: [
          // Tab definitions for Home, Explore, Chat, Notifications, Profile
        ]
      }
    }
  }
  Navigation.setRoot(config)
}
```

Each tab contains:
- A unique stack navigator
- Tab icon configuration
- Default screen options

```mermaid
graph TD
    A[Bottom Tabs] --> B[Home Stack]
    A --> C[Explore Stack]
    A --> D[Chat Stack]
    A --> E[Notifications Stack]
    A --> F[Profile Stack]
    
    B --> B1[Home Screen]
    B --> B2[Other Home Screens...]
    
    C --> C1[Explore Screen]
    C --> C2[Search Results]
    C --> C3[Other Explore Screens...]
    
    D --> D1[Chat List]
    D --> D2[Chat Room]
    D --> D3[Thread]
    
    E --> E1[Notifications List]
    
    F --> F1[Profile Screen]
    F --> F2[Settings]
    F --> F3[Other Profile Screens...]
```

### Stack Navigation

Individual features use stack navigation for sequential screens:

```javascript
// Example of a stack navigation definition
Navigation.setRoot({
  root: {
    stack: {
      id: 'Main',
      options: { topBar: { visible: false } },
      children: [
        {
          component: {
            name: 'Main',
          },
        },
      ],
    },
  },
})
```

```mermaid
flowchart TD
    A[App Entry] --> B{Authenticated?}
    B -->|Yes| C[Home Tabs]
    B -->|No| D[Auth Flow]
    
    D --> D1[Main Screen]
    D --> D2[Login]
    D --> D3[Signup]
    D --> D4[Forgot Password]
    
    D3 --> D3a[Create Profile]
    D3a --> D3b[Follow Suggestions]
    D3b --> C
```

### Navigation Actions

The app defines helper functions for common navigation actions:

- `goMain()`: Navigate to the main entry screen
- `goResetPassword()`: Navigate to password reset flow
- `goCreateProfile()`: Navigate to profile creation flow
- `goHome()`: Navigate to the main tabbed interface

## Deep Linking

The app supports deep linking through a comprehensive system:

### Deep Link Registration

Deep links are registered in `navigation/deepLinks.js` with handlers for:

1. `startDeepLinkEvent()`: Handle links when the app is in the foreground
2. `startDeepLinkBackgroundListener()`: Handle links when launching the app
3. Link processing functions that route to appropriate screens

```mermaid
flowchart TD
    A[Deep Link Received] --> B{App State}
    B -->|Foreground| C[startDeepLinkEvent]
    B -->|Background| D[startDeepLinkBackgroundListener]
    
    C --> E{Authentication Required?}
    D --> E
    
    E -->|Yes| F{User Authenticated?}
    E -->|No| G[processUrl]
    
    F -->|Yes| H[processUrlWithSession]
    F -->|No| I[Show Auth Screen]
    
    G --> J[Navigate to Appropriate Screen]
    H --> J
```

### Deep Link Types

The app handles various deep link patterns:

- Post links: `https://{domain}/post/{id}`
- Profile links: `https://{domain}/profile/{id}`
- Organization links: `https://{domain}/organization/{id}`
- Chat invites: `https://{domain}/chat-invite/{id}`
- Password reset: `unified:/reset_password/{token}`
- Account confirmation: `https://{domain}/confirm-account/{token}`

### Deep Link Processing

Links are processed based on authentication state:

```javascript
// Processing with authentication
export const processUrlWithSession = (url, globalState, setToasts, toastRef) => {
  if (url) {
    // Extract path components
    // Handle different link types
    // Navigate accordingly
  }
}

// Processing without authentication
const processUrl = url => {
  if (url) {
    // Handle limited set of links available without auth
  }
}
```

## Screen-to-Screen Navigation

### Component-level Navigation

Components implement navigation using various patterns:

1. **Direct Navigation**: Using Navigation library methods
   ```javascript
   Navigation.push(componentId, {
     component: {
       name: 'TargetScreen',
       passProps: { /* props */ },
       options: { /* options */ }
     }
   })
   ```

2. **Navigation Helpers**: Custom helper functions
   ```javascript
   // Example pattern in navigation files
   export const executeNavigation = (screenName, componentId, props, isExternal = false, title = '') => {
     // Navigation implementation
   }
   ```

3. **Deep Link Handling**: For handling notification and link-based navigation
   ```javascript
   processNotification(item, globalState)
   ```

```mermaid
sequenceDiagram
    participant Component
    participant NavigationHelper
    participant RNNavigation
    participant TargetScreen
    
    Component->>NavigationHelper: executeNavigation('Profile', componentId, props)
    NavigationHelper->>RNNavigation: Navigation.push(componentId, {...})
    RNNavigation->>TargetScreen: Mount & Pass Props
    TargetScreen->>Component: Return Result (if needed)
```

## Animation and Transitions

The app configures custom animations for screen transitions:

```javascript
// Android animation configuration
animations: {
  push: {
    enabled: true,
    topBar: {
      x: {
        from: require('react-native').Dimensions.get('window').width,
        to: 0,
        duration: 400,
        interpolation: { type: 'accelerateDecelerate' },
      },
    },
    content: {
      // Similar configuration
    },
  },
  pop: {
    // Pop animation configuration
  },
}
```

Platform-specific animations are defined separately for iOS and Android.

## Modal Presentation

The app uses several modal presentation patterns:

1. **Full-screen modals**: For major flows like creation screens
2. **Bottom sheets**: For contextual actions and options
3. **Floating modals**: For alerts and confirmations

```mermaid
graph TD
    A[Modal Types] --> B[Full-screen Modals]
    A --> C[Bottom Sheets]
    A --> D[Floating Modals]
    
    B --> B1[Create Post]
    B --> B2[Profile Edit]
    
    C --> C1[Action Sheet]
    C --> C2[Options Menu]
    
    D --> D1[Alerts]
    D --> D2[Confirmations]
```

## Navigation State Management

Navigation state interacts with the app's state management:

1. **Screen Tracking**: Current screen stored in MMKV
   ```javascript
   globalState.mmkv.set('CURRENT-SCREEN', componentId)
   globalState.mmkv.set('CURRENT-SCREEN-COMPONENT', screenName)
   ```

2. **Navigation Events**: Navigation events trigger state updates
   ```javascript
   Navigation.events().registerComponentDidAppearListener(({componentId, componentName}) => {
     // Update state based on navigation
   })
   ```

3. **Auth-based Navigation**: Navigation changes based on auth state
   ```javascript
   // In app initialization
   const token = await AsyncStorage.getItem(TOKEN)
   if (token) {
     Store.actions.setAuth(token)
     goHome()
   } else {
     // Navigate to authentication flow
   }
   ```

```mermaid
flowchart TD
    A[App Launched] --> B[Check Auth Token]
    B -->|Token Exists| C[Set Auth in Global State]
    B -->|No Token| D[Show Auth Flow]
    
    C --> E[Navigate to Home Tabs]
    
    F[Navigation Events] --> G[Screen Appears]
    G --> H[Update MMKV with Current Screen]
    G --> I[Update Navigation State]
```

## Notification Integration

Navigation is tightly integrated with notifications:

1. **Notification Routing**: `router()` function in `notifications/index.js`
2. **Context-aware Navigation**: Different navigation based on app state
3. **Deep Link Processing**: Notification payloads processed similar to deep links

```mermaid
flowchart TD
    A[Notification Received] --> B[Extract Data]
    B --> C{Resource Type}
    
    C -->|Post| D[Navigate to Post]
    C -->|Profile| E[Navigate to Profile]
    C -->|Chat| F[Navigate to Chat Room]
    C -->|Action| G[Navigate to Action]
    C -->|Organization| H[Navigate to Organization]
    
    F --> F1{Current Screen}
    F1 -->|Already in Chat| F2[Navigate to Specific Message]
    F1 -->|Other Screen| F3[Navigate to Chat First]
```

## Best Practices Implemented

1. **Consistent Patterns**: Similar navigation approaches across features
2. **Separation of Concerns**: Navigation logic separated from components
3. **Wrapper Components**: Common functionality provided via wrappers
4. **Platform Optimizations**: Custom animations and behaviors per platform
5. **State Integration**: Navigation synchronized with app state

## Cross-Feature Navigation

The app implements several patterns for navigating between features:

1. **Profile to Settings**: Navigation from profile to settings screens
2. **Post to User**: Navigation from posts to user profiles
3. **Notification to Content**: Navigation from notifications to relevant content
4. **Deep Link to Feature**: Navigation from external links to specific features