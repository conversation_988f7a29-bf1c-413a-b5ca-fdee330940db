# API and Networking Architecture

## Overview
The Unified Mobile app uses a structured approach to API communication, built around a centralized API client with domain-specific modules. The architecture supports authentication, environment-specific endpoints, and error handling.

```mermaid
graph TD
    A[API Architecture] --> B[Core API Client]
    A --> C[Domain-specific Modules]
    A --> D[HTTP Client]
    A --> E[Authentication]
    
    B --> B1[Environment Management]
    B --> B2[Error Handling]
    B --> B3[Request/Response Processing]
    
    C --> C1[Users API]
    C --> C2[Chat API]
    C --> C3[Posts API]
    C --> C4[Actions API]
    C --> C5[Many other modules...]
    
    D --> D1[Axios Instance]
    D --> D2[Request Configuration]
    D --> D3[Interceptors]
    
    E --> E1[Token Management]
    E --> E2[Session Tracking]
    E --> E3[Auth Methods]
```

## Core API Client

The main API client is defined in `api/Unified/index.js` as a class called `UNIFIEDAPI`. This class:

1. Initializes the API client with authentication
2. Creates domain-specific API modules
3. Provides authentication management
4. Handles environment-specific API endpoints

```javascript
class UNIFIEDAPI {
  constructor(authToken) {
    this._authToken = authToken || null
    this.init(authToken)
  }

  // Additional methods...
}
```

```mermaid
classDiagram
    class UNIFIEDAPI {
        -string _authToken
        -axios http
        +FeedAPI feed
        +ChatAPI chat
        +UsersAPI users
        +ActionsAPI actions
        +constructor(authToken)
        +init(authToken)
        +getHost()
        +auth(username, password)
        +checkTokenValidity()
        +set authToken(token)
        +getAuthToken()
        +getAxiosInstance()
    }
    
    class DomainAPI {
        -axios http
        +constructor(httpClient)
        +methodA()
        +methodB()
    }
    
    UNIFIEDAPI --> "many" DomainAPI
```

## Environment Management

The API client supports multiple environments:

```javascript
async getHost() {
  const host = await AsyncStorage.getItem('APIURL')
  return host
}

async init(authToken) {
  this._authToken = authToken || null
  const host = await this.getHost()

  const axiosOpts = {
    baseURL: host ? host : API_HOST,
    headers: {},
  }
  // Additional configuration...
}
```

- Production: `https://api.prod.unified.community/v1`
- Development: `https://api.dev.unified.community/v1`

```mermaid
flowchart TD
    A[API Client Initialization] --> B{Environment Config Exists?}
    B -->|Yes| C[Use Environment-specific URL]
    B -->|No| D[Use Default Production URL]
    
    C --> E[Initialize Axios with URL]
    D --> E
    
    E --> F{Auth Token Exists?}
    F -->|Yes| G[Add Auth Headers]
    F -->|No| H[No Auth Headers]
    
    G --> I[Initialize Domain APIs]
    H --> I
```

## Domain-Specific API Modules

The API client is organized into domain-specific modules for different features:

```javascript
// In UNIFIEDAPI.init()
this.feed = new FeedAPI(this.http)
this.jobs = new JobsAPI(this.http)
this.chat = new ChatAPI(this.http)
// ... and many more
```

Each module encapsulates related API endpoints:

- `users.js`: User profiles and management
- `chat.js`: Chat and messaging features
- `posts.js`: Social post functionality
- `actions.js`: Civic action endpoints
- And many more specific modules

## Authentication Implementation

The API client handles authentication through a token-based approach:

1. **Token Storage**: Auth tokens stored in AsyncStorage
2. **Token Usage**: Added as bearer tokens in request headers
3. **Token Refresh**: API methods for token validity checking

```javascript
// Setting up authenticated requests
if (this._authToken) {
  axiosOpts.headers = {
    Authorization: `Bearer ${this._authToken}`,
  }
}
```

```mermaid
sequenceDiagram
    participant App
    participant APIClient
    participant Storage
    participant Backend
    
    App->>APIClient: Login(username, password)
    APIClient->>Backend: POST /auth/login
    Backend->>APIClient: Return auth token
    APIClient->>Storage: Store token in AsyncStorage
    APIClient->>App: Return success
    
    Note over APIClient,Backend: Subsequent Authenticated Requests
    
    App->>APIClient: getProfile()
    APIClient->>Storage: Get stored token
    APIClient->>Backend: GET /users/profile with token
    Backend->>APIClient: Return profile data
    APIClient->>App: Return profile
```

Authentication methods include:

- `auth()`: Username/password authentication
- `checkTokenValidity()`: Verify token is still valid
- `getAuthToken()`: Retrieve the current token
- Various registration and password reset methods

## HTTP Client Configuration

The app uses Axios as the HTTP client:

```javascript
this.http = axios.create(axiosOpts)
```

With this configuration:
- Base URL from environment configuration
- Authentication headers when available
- Session tracking headers

## Module Structure Pattern

Each API module follows a similar pattern:

```javascript
// Example pattern from API modules
class ModuleAPI {
  constructor(httpClient) {
    this.http = httpClient
  }

  // Module-specific methods that call API endpoints
  getSomething(id) {
    return core.executeGet(this.http, `/endpoint/${id}`)
  }

  createSomething(data) {
    return core.executePost(this.http, '/endpoint', data)
  }
  
  // Additional methods...
}
```

```mermaid
graph TD
    A[API Module Pattern] --> B[Constructor with HTTP Client]
    A --> C[Individual Endpoint Methods]
    
    C --> D[core.executeGet]
    C --> E[core.executePost]
    C --> F[core.executePut]
    C --> G[core.executeDelete]
    
    D --> H[Error Handling]
    E --> H
    F --> H
    G --> H
    
    H --> I[Response Processing]
    I --> J[Return Result to Component]
```

## Core Utility Methods

The `core.js` module provides common HTTP request handling:

- `executeGet`: Handle GET requests
- `executePost`: Handle POST requests
- `executePut`: Handle PUT requests
- `executeDelete`: Handle DELETE requests

These methods implement standard error handling and response processing.

## Error Handling

API errors are handled through a custom error class (`CostumError.js`) and standardized error handling in the core utilities.

Error handling includes:
1. Network error detection
2. HTTP status code handling
3. Response validation
4. Retry mechanisms (in some components)

## API Integration with State Management

The API client is integrated with the app's state management:

```javascript
// In contexts/store.js
initialState: {
  // Other state...
  api: new UAPI(''),
  // Additional state...
}

actions: {
  setAuth:
    (token = '') =>
      ({ setState, getState }) => {
        AsyncStorage.setItem(TOKEN, token)
        setState({ authToken: token, api: new UAPI(token), mmkv: new MMKV() })
      },
  // Additional actions...
}
```

```mermaid
flowchart LR
    A[Authentication] -->|Token| B[Global State]
    B -->|Update API Client| C[New API Client Instance]
    C -->|Stored in| B
    
    D[Component] -->|Access| B
    D -->|API Methods| C
    C -->|API Responses| D
    C -->|Update| B
```

This integration means:
1. API client is accessible from global state
2. Authentication changes reinitialize the API client
3. API responses can directly update state

## API-Specific Features

### Registration and Auth
The app implements a comprehensive authentication flow:

```javascript
// Example registration methods
startDirectRegistration(email = '', phone = '', inviteCode = '') {
  // Implementation...
}

continueDirectRegistration(workflowId, captcha, fname, lname, password, assetId) {
  // Implementation...
}

confirmDirectRegistration(token) {
  // Implementation...
}
```

### Event Logging
The API includes structured event logging:

```javascript
// In global state actions
logEvent:
  (label = '', view = '', input_type = '', component = '', metadata) =>
    ({ getState }) => {
      try {
        getState().api.logging.logEvent(label, view, input_type, component, metadata)
          .then((response) => console.log('LOGGED EVENT', label, view, input_type, component, metadata, response))
      } catch (e) {}
    },
```

### Session Tracking
API requests include session tracking:

```javascript
setSessionId:
  (session_id = null) =>
    ({ setState, getState }) => {
      const newSessionId = getState().session_id || generateUUID()
      const appVersion = DeviceInfo.getVersion()
      getState().api.getAxiosInstance().defaults.headers.common['session-id'] = newSessionId
      getState().api.getAxiosInstance().defaults.headers.common['app-version'] = appVersion
      setState({ session_id: newSessionId})
      return newSessionId
    },
```

## Data Formatting

API requests use various data formats:

1. JSON for most requests
2. FormData for file uploads and some authentication endpoints

```javascript
// Example FormData usage
auth(username, password) {
  const bodyFormData = new FormData()
  bodyFormData.append('username', username)
  bodyFormData.append('password', password)
  return core.executePost(this.http, '/auth/login', bodyFormData)
}
```

## Media Handling

The app includes specialized handling for media uploads:

- Image compression and formatting
- Video handling
- File upload progress tracking

```mermaid
graph TD
    A[Media Upload] --> B[Compression]
    B --> C[Format Conversion]
    C --> D[Create FormData]
    D --> E[Progress Tracking]
    E --> F[API Upload]
    F --> G[Response Processing]
```

## Network-Aware Components

Components are designed to be network-aware:

1. Loading states during API requests
2. Error handling and retry mechanisms
3. Offline detection and handling

## Best Practices Implemented

1. **Domain Separation**: API functionality split by domain
2. **Centralized Configuration**: Environment and auth handling in one place
3. **Error Standardization**: Consistent error handling
4. **Authentication Management**: Centralized token handling
5. **Environment Awareness**: Support for different API environments