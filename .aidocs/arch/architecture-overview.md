# Unified Mobile App Architecture Overview

## Application Overview
The Unified Mobile app is a React Native application built for both iOS and Android platforms. It appears to be a social/community platform with features for user connections, organization management, chat functionality, and civic engagement tools like contacting representatives and voter registration.

```mermaid
graph TB
    A[Unified Mobile App] --> B[Authentication]
    A --> C[Social Features]
    A --> D[Chat System]
    A --> E[Civic Engagement]
    A --> F[Organizations]
    
    B --> B1[Login/Signup]
    B --> B2[Profile Management]
    B --> B3[Session Handling]
    
    C --> C1[User Profiles]
    C --> C2[Posts & Comments]
    C --> C3[Following/Followers]
    C --> C4[Notifications]
    
    D --> D1[Direct Messages]
    D --> D2[Group Chats]
    D --> D3[Organization Channels]
    
    E --> E1[Contact Representatives]
    E --> E2[Voter Registration]
    E --> E3[Action Tracking]
    
    F --> F1[Org Profiles]
    F --> F2[Member Management]
    F --> F3[Org Chat]
```

## Technology Stack

### Core Framework
- **React Native**: The primary framework for cross-platform mobile development
- **TypeScript/JavaScript**: The codebase includes both TypeScript (.tsx) and JavaScript (.js) files
- **React Native Navigation**: Used for navigation instead of React Navigation
- **React Sweet State**: Used for state management

```mermaid
graph TD
    A[Technology Stack] --> B[Core Libraries]
    A --> C[UI Layer]
    A --> D[Data Layer]
    A --> E[Platform Bridges]
    
    B --> B1[React Native]
    B --> B2[TypeScript/JavaScript]
    B --> B3[React Native Navigation]
    B --> B4[React Sweet State]
    
    C --> C1[React Components]
    C --> C2[Native UI Components]
    C --> C3[Custom UI Elements]
    
    D --> D1[Axios]
    D --> D2[MMKV/AsyncStorage]
    D --> D3[API Client]
    
    E --> E1[Native Modules]
    E --> E2[Third-party SDKs]
    E --> E3[Native Integration]
```

### Key Libraries
- **Axios**: For HTTP requests
- **react-native-mmkv**: For performant key-value storage
- **AsyncStorage**: For persistent storage
- **Lodash**: For utility functions
- **Day.js**: For date formatting and manipulation
- **FontAwesome**: For icons
- **OneSignal**: For push notifications
- **react-native-contacts**: For integrating with device contacts
- **react-native-gesture-handler**: For gesture handling
- **react-native-webview**: For web content

## Application Structure

### Navigation
The app uses React Native Navigation (wix) instead of the more common React Navigation. Navigation is set up in `navigation/index.js` and `navigation/registerScreens.js`. The app implements a tab-based navigation structure with screens for Home, Explore, Chat, Notifications, and Profile.

Navigation between screens is handled through functions like `goHome()`, `goMain()`, etc., which define the navigation stacks and configuration.

```mermaid
graph TD
    A[App Entry] --> B[Check Auth]
    B -->|Authenticated| C[Main Tabs]
    B -->|Not Authenticated| D[Auth Flow]
    
    C --> C1[Home Tab]
    C --> C2[Explore Tab]
    C --> C3[Chat Tab]
    C --> C4[Notifications Tab]
    C --> C5[Profile Tab]
    
    D --> D1[Login]
    D --> D2[Signup]
    D --> D3[Password Reset]
    
    C1 --> C1S[Home Stack]
    C2 --> C2S[Explore Stack]
    C3 --> C3S[Chat Stack]
    C4 --> C4S[Notifications Stack]
    C5 --> C5S[Profile Stack]
```

### State Management
The application uses React Sweet State for global state management:

- `contexts/store.js`: Defines the global state store with initial values and actions
- Component-specific state is managed in local store files (e.g., `components/Profile/LocalStore/index.js`)
- The app uses a combination of global state, local state, and persistent storage (MMKV and AsyncStorage)

```mermaid
graph LR
    A[State Management] --> B[Global State]
    A --> C[Component State]
    A --> D[Persistent Storage]
    
    B --> B1[Auth Token]
    B --> B2[User Profile]
    B --> B3[API Client]
    B --> B4[App Settings]
    
    C --> C1[UI State]
    C --> C2[Form State]
    C --> C3[Local Data]
    
    D --> D1[MMKV]
    D --> D2[AsyncStorage]
    
    D1 --> D1A[Cached Profile]
    D1 --> D1B[Settings]
    D1 --> D1C[UI Preferences]
    
    D2 --> D2A[Auth Token]
    D2 --> D2B[Larger Objects]
    D2 --> D2C[Environment Config]
```

### API Communication
The app communicates with a backend service through a structured API client:

- `api/Unified/index.js`: Main API client class that initializes different API modules
- Each API module (e.g., `users.js`, `chat.js`, `posts.js`) handles a specific domain of backend functionality
- API calls are made through Axios with authentication handled via bearer tokens

### Component Structure
The application follows a feature-based structure:

- `components/`: Contains all UI components organized by feature
- Each feature typically has:
  - An `index.js` as the main component file
  - A `styles.js` for styling
  - Sub-components in sub-directories
  - A `LocalStore` for component-specific state
  - Navigation-related functions

```mermaid
graph TD
    A[Components] --> B[Feature-based Organization]
    
    B --> C[Profile]
    B --> D[Chat]
    B --> E[Feed]
    B --> F[Explore]
    B --> G[Common UI Elements]
    
    C --> C1[index.js]
    C --> C2[styles.js]
    C --> C3[LocalStore]
    C --> C4[Sub-components]
    
    C4 --> C4A[Header]
    C4 --> C4B[Banner]
    C4 --> C4C[ActionFeed]
    
    G --> G1[Buttons]
    G --> G2[Inputs]
    G --> G3[Cards]
    G --> G4[Modal Components]
```

### Key Features

#### Authentication
- Login, signup, and password reset flows
- Token-based authentication
- User profile creation and management

#### Chat System
- Direct messaging (DM)
- Group chats with member management
- Organization chats with privacy settings
- Thread-based conversations

#### Social Features
- User profiles with posts, actions, and impact metrics
- Following/follower system
- Post creation with media support
- Comment system
- Notifications for social interactions

#### Organizations
- Organization profiles
- Member management
- Join requests and invitations
- Organization-specific chats

#### Civic Engagement
- Contact representatives (call, email)
- Voter registration tools
- Actions and campaigns tracking

#### Deep Linking
- The app supports deep linking for various resources (posts, profiles, etc.)
- Implementation in `navigation/deepLinks.js`
- Handles both background and foreground link opening

### Theme Support
- The app supports both light and dark mode using React Native's `useColorScheme`
- Styles are generated based on the current theme using StyleSheetFactory functions
- Color definitions in `components/Utils/colors.js`

## Environment Configuration
- Production and development environments with separate API endpoints
- Environment detection via AsyncStorage key "APIURL"
- Deep links with environment-specific domains:
  - Production: unified.me
  - Development: dev.unified.me

## Data Persistence
- AsyncStorage for larger, less frequently accessed data
- MMKV for performant key-value storage
- Structured caching strategies for profile and session data

## Notable Architectural Patterns

### Component Composition
- Components are composed from smaller, reusable components
- Higher-order components used for wrapping (like Toast notifications)
- Feature-specific components organized in feature directories

### State Management
- Combination of global state (for auth, profile, etc.) and local component state
- Reactive updates through state subscriptions
- Persistent storage with synchronization strategies

### API Client Structure
- Object-oriented API client with domain-specific modules
- Authentication token management and request interceptors
- Error handling and retries

### Navigation Management
- Declarative screen registration
- Programmatic navigation through navigation functions
- Deep linking support with routing logic

## Code Style and Conventions
- PascalCase for component names
- camelCase for variables and functions
- Component files organized with index.js and styles.js
- Feature-based folder structure
- Functional components with hooks