# Component Structure and Patterns

## Overview
The Unified Mobile app implements a feature-based component structure with consistent patterns for component organization, styling, and state management. This document outlines the component architecture used throughout the application.

```mermaid
graph TD
    A[Component Architecture] --> B[Organization Structure]
    A --> C[Component Patterns]
    A --> D[Styling Approach]
    A --> E[State Integration]
    
    B --> B1[Feature-based Folders]
    B --> B2[File Conventions]
    B --> B3[Component Hierarchies]
    
    C --> C1[Functional Components]
    C --> C2[React Hooks]
    C --> C3[Component Composition]
    
    D --> D1[StyleSheetFactory]
    D --> D2[Theme Support]
    D --> D3[Responsive Design]
    
    E --> E1[Global State]
    E --> E2[Component State]
    E --> E3[Persistence]
```

## Component Organization

### Feature-Based Structure

Components are organized by feature rather than by type:

```
components/
  ├── Profile/               # Profile feature
  │   ├── index.js           # Main component
  │   ├── styles.js          # Styling
  │   ├── Banner/            # Sub-component
  │   ├── Header/            # Sub-component
  │   ├── LocalStore/        # Feature-specific state
  │   └── navigation.js      # Feature-specific navigation
  │
  ├── Chat/                  # Chat feature
  │   ├── index.js
  │   ├── styles.js
  │   ├── Room/
  │   ├── Thread/
  │   └── ...
```

This organization:
1. Groups related components together
2. Isolates feature-specific code
3. Makes feature boundaries clear

```mermaid
classDiagram
    class FeatureComponent {
        +index.js
        +styles.js
        +LocalStore/
        +navigation.js
        +sub-components/
    }
    
    class SubComponent {
        +index.js
        +styles.js
    }
    
    class LocalStore {
        +index.js
        +initialState
        +actions
    }
    
    FeatureComponent *-- SubComponent : Contains
    FeatureComponent *-- LocalStore : Has State
```

### Component File Structure

Most component directories follow a consistent pattern:

- `index.js`: The main component implementation
- `styles.js`: Component styling (often with theme support)
- Sub-directories for child components
- `LocalStore/`: Feature-specific state management
- `navigation.js`: Feature-specific navigation functions

## Component Implementation Patterns

### Functional Components with Hooks

The app uses modern React patterns with functional components and hooks:

```javascript
// Example pattern from components
const Profile = ({ externalUser = null, popper, componentId, updateAddress = false, reloadCB = null }) => {
  // State hooks
  const [tab, setTab] = useState('Posts')
  const [edit, setEdit] = useState(false)
  
  // Effect hooks
  useEffect(() => {
    // Implementation
  }, [dependencies])
  
  // Custom hooks
  const [globalState, globalActions] = useGlobalState()
  const [localState, localActions] = useProfileState()
  
  // Component logic
  
  // Render
  return (
    <View>
      {/* Component JSX */}
    </View>
  )
}
```

```mermaid
graph TD
    A[Functional Component] --> B[Props]
    A --> C[Hooks]
    A --> D[Local Functions]
    A --> E[JSX Rendering]
    
    C --> C1[useState]
    C --> C2[useEffect]
    C --> C3[useCallback]
    C --> C4[useRef]
    C --> C5[Custom Hooks]
    
    C5 --> C5a[useGlobalState]
    C5 --> C5b[useProfileState]
    C5 --> C5c[useMMKVObject]
    
    D --> D1[Event Handlers]
    D --> D2[Data Processing]
    D --> D3[Helper Functions]
    
    E --> E1[Component Composition]
    E --> E2[Conditional Rendering]
    E --> E3[Style Application]
```

### Styling Approach

Components implement styling through StyleSheet factories that support theming:

```javascript
// Example pattern from style files
const StyleSheetFactory = {
  getSheet: (isDark) => StyleSheet.create({
    container: {
      backgroundColor: isDark ? darkModeGray : 'white',
      // More styles
    },
    // More style definitions
  })
}

// Used in components
const styles = StyleSheetFactory.getSheet(isDarkMode)
```

This enables consistent theme support across the app.

```mermaid
flowchart TD
    A[Component Rendering] --> B{isDarkMode?}
    B -->|Yes| C[Dark Theme Styles]
    B -->|No| D[Light Theme Styles]
    
    C --> E[Apply Styles to Elements]
    D --> E
    
    F[StyleSheetFactory] --> G[getSheet Method]
    G --> H[StyleSheet.create]
    H --> I[Generated Styles]
    I --> C
    I --> D
```

## Component Types

### Screen Components

Top-level components that represent full screens:

- Registered with the Navigation library
- Often contain multiple child components
- Handle screen-level state and logic
- Examples: `Profile`, `Home`, `Chat`

### Feature Components

Major functional units within screens:

- Handle specific feature functionality
- Often composed of multiple UI components
- Examples: `Banner`, `ActionFeed`, `UserList`

### UI Components

Reusable UI elements:

- Generic and reusable across features
- Focus on presentation rather than business logic
- Examples: `LargeButton`, `UText`, `Pill`

### Specialized Components

Components with specific technical purposes:

- `CachedImage`: Optimized image loading
- `BackgroundComponent`: Background handling
- `VideoPlayer`: Media playback

```mermaid
graph TD
    A[Component Types] --> B[Screen Components]
    A --> C[Feature Components]
    A --> D[UI Components]
    A --> E[Specialized Components]
    
    B --> B1[Profile]
    B --> B2[Home]
    B --> B3[Chat]
    
    C --> C1[Banner]
    C --> C2[ActionFeed]
    C --> C3[UserList]
    
    D --> D1[LargeButton]
    D --> D2[UText]
    D --> D3[Pill]
    
    E --> E1[CachedImage]
    E --> E2[VideoPlayer]
    E --> E3[BackgroundComponent]
```

## Component Communication

### Props

The primary method of component communication:

```javascript
// Example prop passing
<Banner
  profile={profile}
  externalUser={externalUser}
  reloadCB={reloadCB}
  updateAddress={updateAddress}
  blurness={blurness}
  totalHeight={100}
  isMainUser={isMainUser}
  edit={edit}
  setEdit={setEdit}
  popper={popper}
  ref={bannerRef}
  navigateToSettings={navigateToSettings}
/>
```

### Context

Used for deeply nested component access:

- Global state through Sweet State
- Theme information through context
- Navigation context for component IDs

### State Management

Described in detail in the State Management document. Key points:

- Global state for app-wide concerns
- Component-specific state for isolated features
- Persistent storage for data that survives app restarts

```mermaid
sequenceDiagram
    participant ParentComponent
    participant ChildComponent
    participant GlobalState
    
    ParentComponent->>ChildComponent: Pass Props
    ParentComponent->>ChildComponent: Pass Callbacks
    ChildComponent->>ParentComponent: Invoke Callbacks
    
    ParentComponent->>GlobalState: Read/Update Global State
    ChildComponent->>GlobalState: Read/Update Global State
    
    GlobalState->>ParentComponent: State Updates
    GlobalState->>ChildComponent: State Updates
```

## Animation Patterns

### Animated API

The app uses React Native's Animated API for UI animations:

```javascript
// Example animation pattern
const top = useRef(new Animated.Value(-42)).current

const animateValue = (target, toValue, config) => {
  Animated.spring(target, {
    ...config,
    toValue,
    useNativeDriver: false,
  }).start()
}

// Used in scroll handling
if (e.contentOffset.y >= 50) {
  animateValue(blurness, 1.0, animationConfig)
  animateValue(top, 0, animationConfig)
  // More animations
}
```

### Performance Optimizations

For performance, the app implements several optimization patterns:

- `debounce` for frequent events like scrolling
- `useRef` for values that don't trigger re-renders
- `useCallback` for stable function references

```mermaid
graph TD
    A[Animation Triggers] --> B[User Interaction]
    A --> C[Navigation]
    A --> D[Data Loading]
    
    B --> B1[Scroll Events]
    B --> B2[Tab Selection]
    B --> B3[Button Press]
    
    E[Animation Types] --> E1[Property Animations]
    E --> E2[Layout Animations]
    E --> E3[Transition Animations]
    
    F[Animation Values] --> F1[Animated.Value]
    F --> F2[useRef refs]
    
    G[Animation Methods] --> G1[Animated.spring]
    G --> G2[Animated.timing]
```

## Composition Patterns

### Component Composition

The app uses component composition extensively:

```javascript
// Composition pattern example
<View style={{ flex: 1, overflow: 'hidden', backgroundColor: monochroma2 }}>
  <Banner
    profile={profile}
    /* Other props */
  />
  <FlatList
    /* FlatList props */
    renderItem={({ item, index }) => {
      if (item.id === 0) {
        return <ComponentA />
      }
      if (item.id === 1) {
        return <ComponentB />
      }
      // More conditional rendering
    }}
  />
  {isMainUser && !edit && <FloatingMenu componentId={componentId} />}
</View>
```

### Conditional Rendering

Heavy use of conditional rendering for UI states:

```javascript
// Conditional rendering pattern
{!isItVisible() && (
  <View style={{ zIndex: 999 }}>
    <UserBase textMargin={textMargin} top={top} diameter={diameter} edit={edit} profile={profile} />
  </View>
)}
```

### Component Wrapping

Components are often wrapped for additional functionality:

```javascript
// Wrapper pattern from registerScreens.js
const wrapWithWrapper = Component =>
  gestureHandlerRootHOC(props => (
    <ToastStackWrapper>
      <Component {...props} />
    </ToastStackWrapper>
  ))
```

```mermaid
graph TD
    A[Component Composition] --> B[Container Components]
    A --> C[Presentational Components]
    A --> D[Higher-Order Components]
    A --> E[Render Props]
    
    B --> B1[Layout Containers]
    B --> B2[Data Containers]
    
    C --> C1[UI Elements]
    C --> C2[Display Components]
    
    D --> D1[ToastStackWrapper]
    D --> D2[gestureHandlerRootHOC]
    
    E --> E1[FlatList renderItem]
    E --> E2[Conditional Renders]
```

## Responsive Design

The app implements responsive design through:

1. **Flexible layouts**: Using flex properties
2. **Responsive units**: Avoiding hardcoded pixel values where possible
3. **Device detection**: Adapting to different screen sizes
4. **Platform-specific code**: Different implementations for iOS and Android

## Theme Support

Theme support is implemented through:

1. **useColorScheme**: React Native's hook for detecting system theme
2. **StyleSheetFactory**: Creating styles based on theme
3. **Theme variables**: Colors defined in `components/Utils/colors.js`
4. **Dynamic properties**: Using theme values in component props

```javascript
// Theme implementation pattern
const isDarkMode = useColorScheme() === 'dark'
const styles = StyleSheetFactory.getSheet(isDarkMode)
const monochroma = isDarkMode ? 'white' : 'black'
const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'
```

```mermaid
flowchart TD
    A[Theme System] --> B[Theme Detection]
    A --> C[Color Definitions]
    A --> D[Theme Application]
    
    B --> B1[useColorScheme]
    
    C --> C1[colors.js]
    C1 --> C1a[Light Theme Colors]
    C1 --> C1b[Dark Theme Colors]
    
    D --> D1[StyleSheetFactory]
    D --> D2[Dynamic Properties]
    D --> D3[Component-specific Adjustments]
```

## Form Components

The app includes several specialized form components:

- `LargeInput`: Standard text input
- `UCombo`: Combo box/dropdown
- `URadio`: Radio button selection
- `UDatePicker`: Date selection

These components maintain consistent styling and behavior.

## List Components

Lists are implemented using several patterns:

- `FlatList`: For efficient list rendering
- `SectionList`: For grouped data
- Custom list components for specific features

## Modal Components

Several modal implementation patterns:

- `ActionSheet`: For bottom action sheets
- `FullScreenSheet`: For full-screen modal content
- `Modal` from 'react-native-modal': For customized modals

## Media Components

Specialized components for media handling:

- `CachedImage`: For efficient image loading and caching
- `VideoPlayer`: For video playback
- `ImageZoom`: For zoomable images
- `ZoomableImage`: Another implementation of zoomable images

```mermaid
graph TD
    A[Special Component Types] --> B[Form Components]
    A --> C[List Components]
    A --> D[Modal Components]
    A --> E[Media Components]
    
    B --> B1[LargeInput]
    B --> B2[UCombo]
    B --> B3[URadio]
    B --> B4[UDatePicker]
    
    C --> C1[FlatList]
    C --> C2[SectionList]
    C --> C3[Custom Lists]
    
    D --> D1[ActionSheet]
    D --> D2[FullScreenSheet]
    D --> D3[Modal Components]
    
    E --> E1[CachedImage]
    E --> E2[VideoPlayer]
    E --> E3[ImageZoom]
    E --> E4[ZoomableImage]
```

## Best Practices Implemented

1. **Consistent Structure**: Similar organization across features
2. **Component Isolation**: Components focused on specific functionality
3. **Reusable Components**: Common UI elements shared across features
4. **Performance Optimizations**: Techniques to minimize render impact
5. **Responsive Design**: Adapting to different device sizes and orientations