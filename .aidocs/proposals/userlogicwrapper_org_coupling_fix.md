# Refactoring Proposal: Organization Logic in UserLogicWrapper

## Current Issues

The `UserLogicWrapper.js` component currently contains tightly coupled logic for determining which buttons to display for organizations, mixing several concerns:

1. Organization state determination (`isOrganization`, `isNotMember`, etc.)
2. Join request status management 
3. Button configuration logic
4. UI rendering

This creates several problems:
- Decreased readability and maintainability
- Difficult to test organization-specific logic
- Logic reuse is challenging when needed in other components
- Growing complexity as more organization-related features are added

## Proposed Solutions

We've evaluated three approaches to refactor this code and improve separation of concerns.

### Proposal 1: Organization View Model Pattern

Create a dedicated class to encapsulate all organization-related logic:

```javascript
// OrganizationViewModel.js
export class OrganizationViewModel {
  constructor(organization, joinRequestsManager) {
    this.organization = organization;
    this.joinRequestsManager = joinRequestsManager;
  }
  
  get isOrganization() {
    return this.organization?.account?.kind === 'organization';
  }
  
  get isMember() {
    return this.isOrganization && this.organization?.is_member === true;
  }
  
  get hasRequestedJoin() {
    return this.joinRequestsManager.hasRequestedJoin(this.organization?.account?.id);
  }
  
  get canRequestJoin() {
    return this.isOrganization && !this.isMember && !this.hasRequestedJoin;
  }
  
  get showRequestSent() {
    return this.isOrganization && !this.isMember && this.hasRequestedJoin;
  }
  
  getButtonOptions() {
    const buttons = {};
    
    if (this.canRequestJoin) {
      buttons.firstButton = {
        title: 'Request to Join',
        cb: () => this.joinRequestsManager.requestToJoin(this.organization),
        color: 'unifiedPurple',
      };
    }
    
    if (this.showRequestSent) {
      buttons.firstButton = {
        title: 'Request Sent',
        cb: () => {}, 
        color: 'unifiedDarkGray',
      };
    }
    
    // Add follow/following button for all organizations
    buttons.secondButton = {
      title: this.organization?.is_following ? 'Following' : 'Follow',
      cb: () => this.joinRequestsManager.toggleFollow(this.organization),
      color: this.organization?.is_following ? 'unifiedDarkGray' : 'unifiedPurple',
    };
    
    return buttons;
  }
}
```

**Advantages:**
- Clear separation of organization logic
- Easily testable with unit tests
- Can be reused across different components

**Disadvantages:**
- Less integrated with React's state management
- Requires additional bridging to React components
- Potentially unfamiliar pattern for React developers

### Proposal 2: Hook-Based Approach

Create a custom hook to manage organization state and button configuration:

```javascript
// useOrganizationState.js
export function useOrganizationState(item, globalState, globalActions) {
  const [hasRequestedJoin, setHasRequestedJoin] = useState(false);
  const [following, setFollowing] = useState(item?.is_following || false);
  
  // Check for existing join requests
  useEffect(() => {
    if (item?.account?.kind === 'organization' && item?.account?.id) {
      const orgId = item.account.id;
      if (isOrgRequestedToJoin(globalState.mmkv, orgId)) {
        setHasRequestedJoin(true);
      }
    }
  }, [item?.account?.id, item?.account?.kind, globalState.mmkv]);
  
  // Update following state
  useEffect(() => {
    setFollowing(!!item?.is_following);
  }, [item?.is_following]);
  
  // Derived organization states
  const organizationState = useMemo(() => ({
    isOrganization: item?.account?.kind === 'organization',
    isMember: item?.is_member === true,
    isFollowing: following,
    hasRequestedJoin,
    
    // Derived states
    canRequestJoin: item?.account?.kind === 'organization' && 
                   item?.is_member === false && 
                   !hasRequestedJoin,
    showRequestSent: item?.account?.kind === 'organization' && 
                    item?.is_member === false && 
                    hasRequestedJoin,
  }), [item?.account?.kind, item?.is_member, following, hasRequestedJoin]);
  
  // Request to join handler
  const handleRequestToJoin = useCallback(async () => {
    try {
      const orgId = item?.account?.id;
      if (!orgId) {
        console.log('Error: No organization ID found');
        return;
      }

      const result = await globalState.api.orgs.requestToJoin(orgId);
      
      // Update local state
      setHasRequestedJoin(true);
      
      // Add to MMKV cache
      addOrgToJoinRequests(globalState.mmkv, orgId);
      
      if (globalActions?.showToast) {
        globalActions.showToast('Request to join sent successfully');
      }
    } catch (e) {
      console.log('ERROR REQUESTING TO JOIN:', JSON.stringify(e));
      
      const errorMessage = e?.info?.responseBody?.message || '';
      const isAlreadyRequested = e?.info?.status === 409 || 
                                errorMessage.includes('Join request already exists');
      
      if (isAlreadyRequested) {
        setHasRequestedJoin(true);
        
        const orgId = item?.account?.id;
        if (orgId) {
          addOrgToJoinRequests(globalState.mmkv, orgId);
        }
      } else if (globalActions?.showToast) {
        globalActions.showToast('Failed to send join request');
      }
    }
  }, [item?.account?.id, globalState, globalActions]);
  
  // Toggle follow handler
  const handleToggleFollow = useCallback(async () => {
    const newFollowingState = !following;
    setFollowing(newFollowingState);
    
    try {
      const aid = item?.account?.id;
      if (newFollowingState) {
        await globalState.api.followers.followUser(aid);
      } else {
        await globalState.api.followers.unfollowUser(aid);
      }
    } catch (e) {
      console.log('ERROR TOGGLING FOLLOW:', e);
      // Revert on error
      setFollowing(following);
    }
  }, [following, item?.account?.id, globalState]);
  
  // Get button configuration
  const getButtonConfig = useCallback(() => {
    if (organizationState.isOrganization) {
      return {
        ...(organizationState.canRequestJoin && {
          firstButton: {
            title: 'Request to Join',
            cb: handleRequestToJoin,
            color: 'unifiedPurple',
          },
        }),
        ...(organizationState.showRequestSent && {
          firstButton: {
            title: 'Request Sent',
            cb: () => {}, 
            color: 'unifiedDarkGray',
          },
        }),
        secondButton: {
          title: organizationState.isFollowing ? 'Following' : 'Follow',
          cb: handleToggleFollow,
          color: organizationState.isFollowing ? 'unifiedDarkGray' : 'unifiedPurple',
        },
      };
    }
    
    return null;
  }, [organizationState, handleRequestToJoin, handleToggleFollow]);
  
  return {
    organizationState,
    getButtonConfig,
    handleRequestToJoin,
    handleToggleFollow,
  };
}
```

**Advantages:**
- Integrates well with React's functional component paradigm
- Separates logic while maintaining React patterns
- Can reuse the hook across different components
- Clear management of related state and effects

**Disadvantages:**
- Organization logic spread across hook implementation
- Dependent on React context

### Proposal 3: Component-Based Approach with Render Props

Create specialized components for organization UI:

```javascript
// OrganizationButtons.js
const OrganizationButtons = ({ 
  organization, 
  onRequestJoin, 
  onToggleFollow,
  requestSentStyle,
  requestJoinStyle,
  followStyle
}) => {
  const [hasRequestedJoin, setHasRequestedJoin] = useState(false);
  const [globalState] = useGlobalState();
  
  useEffect(() => {
    if (organization?.account?.id) {
      const requested = isOrgRequestedToJoin(globalState.mmkv, organization.account.id);
      setHasRequestedJoin(requested);
    }
  }, [organization, globalState.mmkv]);
  
  const isOrganization = organization?.account?.kind === 'organization';
  const isMember = isOrganization && organization?.is_member === true;
  const canRequestJoin = isOrganization && !isMember && !hasRequestedJoin;
  const showRequestSent = isOrganization && !isMember && hasRequestedJoin;
  
  if (!isOrganization) return null;
  
  return {
    ...(canRequestJoin && {
      firstButton: {
        title: 'Request to Join',
        cb: () => onRequestJoin(organization),
        color: 'unifiedPurple',
        ...requestJoinStyle
      },
    }),
    ...(showRequestSent && {
      firstButton: {
        title: 'Request Sent',
        cb: () => {}, 
        color: 'unifiedDarkGray',
        ...requestSentStyle
      },
    }),
    secondButton: {
      title: organization.is_following ? 'Following' : 'Follow',
      cb: () => onToggleFollow(organization),
      color: organization.is_following ? 'unifiedDarkGray' : 'unifiedPurple',
      ...followStyle
    },
  };
};
```

**Advantages:**
- Explicit UI focus
- Customizable via props
- Easily composable with other components

**Disadvantages:**
- Less focus on business logic
- Requires additional props for handlers
- Harder to test complex logic interactions

## Recommendation

**Note: We are leaning towards the hook-based approach (Proposal 2).**

The hook-based approach provides the best balance of:

1. **React integration** - Works naturally with React's functional components and state management
2. **Testability** - Can be unit tested with React Testing Library
3. **Reusability** - Can be used across different components that need organization logic
4. **Separation of concerns** - Cleanly separates organization logic while keeping React patterns

## Implementation Plan

1. Create a new file `/hooks/useOrganizationState.js` with the hook implementation
2. Update `UserLogicWrapper.js` to use the new hook
3. Replace the current `defaultConfig` function with the hook's `getButtonConfig` return value
4. Test the implementation with different organization states
5. Apply the hook to other components that need organization state management

## Migration Strategy

The migration can be done incrementally:

1. First, implement the hook without changing existing code
2. Then, gradually replace the inline logic in `UserLogicWrapper.js` with the hook
3. Test each step to ensure behavior remains consistent
4. Finally, once stable, clean up any redundant code

This approach minimizes risk while improving the codebase architecture.