# Organization Display Improvement Options

This document explores potential improvements to how organizations are displayed in the Profile's "Orgs" tab, based on detailed analysis of the current implementation.

## Current Implementation Analysis

In the Profile's "Orgs" tab, organizations are displayed through a component chain:

```
Profile → OrganizationsList → UserList → UserLogicWrapper → GenericUserHolder → UserHolder
```

Key aspects of the current implementation:

1. **Button Visibility**:
   - When viewing your own organizations in the Profile tab, all buttons are hidden (`noButton={isMainUser && isFromProfile}`)
   - In Organizations Access screen, "Manage" or "View" buttons are shown based on user role
   
2. **Navigation**:
   - Clicking an organization navigates to the organization profile via `navigateToOrg`
   - No quick actions are available from the Profile's Orgs tab

3. **Button Configuration**:
   - UserLogicWrapper has a sophisticated button configuration system
   - OrganizationsList overrides button configuration with `getOrgButtonOptions` but only when not in Profile view
   
4. **Organization Data**:
   - Organizations are fetched via API calls to either `getCurrentUserOrgs()` or `getUserOrgs(profileId)`
   - Data is cached for main user in MMKV storage

## Improvement Opportunities

### 1. Enhanced Visual Hierarchy

Currently, all organizations are displayed identically in the Profile's Orgs tab, without distinguishing user's role in each organization.

**Proposed Improvement**:
```javascript
// Add to OrganizationsList
const getItemStyle = (org) => {
  // Add subtle visual distinction for orgs where user is admin/owner
  if (isFromProfile && (org.role === 'admin' || org.role === 'owner')) {
    return { 
      borderLeftWidth: 3,
      borderLeftColor: unifiedPurple,
      paddingLeft: 6
    };
  }
  return {};
};

// Then in render:
<UserList
  data={orgs}
  componentId={componentId}
  noButton={isMainUser && isFromProfile}
  itemStyle={item => ({
    ...(isFromProfile ? getItemStyle(item) : {}),
    ...(!isFromProfile ? { marginHorizontal: 0 } : {})
  })}
  buttonOptions={getOrgButtonOptions}
/>
```

### 2. Role Badge Display

Add a visual indicator of the user's role in each organization.

**Proposed Improvement**:
```javascript
// Modify GenericUserHolder to accept and display a badge
<GenericUserHolder
  name={item?.display_name}
  subtitle={item?.profile?.bio}
  badge={item?.role === 'owner' ? 'Owner' : 
         item?.role === 'admin' ? 'Admin' : null}
  badgeColor={unifiedPurple}
  // other props
/>
```

### 3. Conditional Button Display in Profile

Instead of hiding all buttons in the Profile's Orgs tab, selectively show relevant actions.

**Proposed Improvement**:
```javascript
// In OrganizationsList
const getProfileButtonOptions = (org) => {
  // Only show for orgs where user is admin/owner
  if (isMainUser && isFromProfile && (org.role === 'admin' || org.role === 'owner')) {
    return {
      firstButton: {
        title: 'Manage',
        cb: () => navigateToOrgManagement(org),
        color: unifiedPurple,
      }
    };
  }
  
  // For other orgs in profile view, don't show buttons
  return null;
};

// Then in render:
<UserList
  data={orgs}
  componentId={componentId}
  noButton={false} // Allow buttons to be shown
  buttonOptions={isFromProfile ? getProfileButtonOptions : getOrgButtonOptions}
/>
```

### 4. Optimized Organization Data Structure

The current implementation passes the entire organization object down through components. Consider creating a normalized data structure.

**Proposed Improvement**:
```javascript
// In OrganizationsList
const normalizeOrgData = (orgs) => {
  return orgs.map(org => ({
    id: org.account.id,
    name: org.display_name,
    bio: org.profile?.bio,
    profilePic: org.profile_pic,
    role: org.role,
    isFollowing: org.is_following,
    isMember: org.is_member,
    accountType: 'organization'
  }));
};

// Then use normalized data:
<UserList
  data={normalizeOrgData(orgs)}
  // other props
/>
```

### 5. Performance Optimization

The current implementation has some inefficient patterns that could be optimized.

**Proposed Improvements**:
```javascript
// In OrganizationsList
// Use useCallback for navigation functions
const navigateToOrgManagement = useCallback((org) => {
  console.log('NAVIGATE TO ORG', org);
  executeNavigation('OrgManagement', componentId, { org, popper: componentId }, 'Manage organization', false);
}, [componentId]);

// Use useMemo for button configuration
const buttonOptionsFunction = useMemo(() => {
  return isFromProfile ? getProfileButtonOptions : getOrgButtonOptions;
}, [isFromProfile, getProfileButtonOptions, getOrgButtonOptions]);
```

### 6. Refactoring Organization vs User Logic

The UserLogicWrapper currently handles both users and organizations. Consider separating these concerns.

**Proposed Architecture**:
```
                     ┌─► UserLogicWrapper ─► GenericUserHolder
                     │
UserList ─► ItemWrapper ┼─► OrgLogicWrapper ─► GenericUserHolder
                     │
                     └─► GroupLogicWrapper ─► GenericUserHolder
```

This would allow each type to have its own specific logic while reusing the GenericUserHolder UI component.

## Implementation Priority

Based on complexity and impact, here's a suggested priority for these improvements:

1. **Enhanced Visual Hierarchy** - Relatively simple change with high visual impact
2. **Conditional Button Display** - Improves functionality in Profile's Orgs tab
3. **Role Badge Display** - Adds valuable context with minimal complexity
4. **Performance Optimization** - Improves app responsiveness
5. **Optimized Data Structure** - Requires more extensive changes
6. **Refactoring Organization Logic** - Largest architectural change, needs careful planning

## Mockup of Improved Organization Display

```
┌─────────────────────────────────────────────┐
│ ┌───┐                               Manage  │
│ │   │ Organization Name                     │
│ └───┘ Organization Description     ADMIN    │
└─────────────────────────────────────────────┘
┌─────────────────────────────────────────────┐
│ ┌───┐                                       │
│ │   │ Another Organization                  │
│ └───┘ Another Description        MEMBER     │
└─────────────────────────────────────────────┘
```

This mockup shows:
- Visual distinction for admin/owner organizations (left border)
- Role badge to indicate membership status
- Selective button display for organizations where user has management privileges

## Final Thoughts

The current implementation has a solid foundation but could benefit from these targeted improvements to enhance both visual clarity and functionality. The main goals should be:

1. Make organization roles more visually apparent
2. Provide quick access to management functions when applicable
3. Maintain a clean, uncluttered interface
4. Separate organization-specific logic from user display logic

These changes would significantly improve the user experience when interacting with organizations in the Unified Mobile app while maintaining the existing component architecture.