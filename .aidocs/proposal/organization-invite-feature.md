# Organization Invite Feature Implementation Plan

## Overview
This document outlines the implementation plan for adding an "Invite to organizations" feature to the user profile options in the Unified mobile app. The feature allows users with appropriate permissions to invite other users to multiple organizations simultaneously.

## Feature Requirements
1. Add "Invite to organizations" option to user profile options menu
2. Create a modal/sheet showing organizations where the current user can invite people
3. Display organizations with checkboxes for selection
4. Allow multiple organization selection
5. Send invites to selected organizations
6. Show success/error feedback using the Toast system

## Technical Architecture

### Component Structure
```
Profile/Options
└── handleInviteToOrganizations()
    └── OrganizationInviteModal
        ├── Header (title + close)
        ├── OrganizationsList (with checkboxes)
        │   └── UserList (configured for checkbox mode)
        │       └── GenericUserHolder (with URadio-style checkbox)
        └── ActionBar (Cancel + Invite buttons)
```

### State Management
```javascript
// LocalStore state shape
{
  isModalVisible: false,
  userOrganizations: [],
  selectedOrgIds: [],
  isLoading: false,
  isSending: false,
  targetUserId: null,
  targetUserName: ''
}
```

## Implementation Phases

### Phase 1: Options Menu Integration

#### Task 1.1: Update Profile Options Component
**File**: `components/Profile/Options/index.js`
- Add "Invite to organizations" option at the top of the menu
- Use `faUserPlus` icon from Font Awesome
- Create placeholder handler function
- Pass user profile data to the handler

#### Task 1.2: Create Modal Component Structure
**File**: `components/Profile/OrganizationInviteModal/index.js`
- Create base modal component using ActionSheet
- Set up header with title showing user's name
- Add close button functionality
- Create footer with Cancel/Invite buttons

#### Task 1.3: Create LocalStore for State
**File**: `components/Profile/OrganizationInviteModal/LocalStore/index.js`
- Define state structure
- Create actions for:
  - `showModal(userId, userName)`
  - `hideModal()`
  - `toggleOrganization(orgId)`
  - `clearSelections()`
  - `loadUserOrganizations()`
  - `sendInvitations()`

### Phase 2: Organization List Implementation

#### Task 2.1: Adapt OrganizationsList for Checkbox Mode
**File**: `components/Profile/OrganizationInviteModal/InviteOrganizationsList/index.js`
- Extend existing OrganizationsList component
- Pass custom `buttonOptions` for checkbox display
- Handle selection state via LocalStore
- Filter to show only organizations where user can invite

#### Task 2.2: Configure UserList for Checkboxes
**Configuration**:
```javascript
const checkboxButtonOptions = (org) => ({
  firstButton: {
    title: selectedOrgIds.includes(org.account.id) ? '☑' : '☐',
    cb: () => toggleOrganization(org.account.id),
    color: selectedOrgIds.includes(org.account.id) ? unifiedPurple : unifiedGray
  }
})
```

#### Task 2.3: Style Checkbox Using URadio Pattern
- Use existing radio button images but style as checkboxes
- Apply proper colors for selected/unselected states
- Ensure touch targets are adequate

### Phase 3: API Integration

#### Task 3.1: Fetch User's Organizations with Invite Permissions
**API Call**: Use existing `getCurrentUserOrgs()`
**Filtering**:
```javascript
const invitableOrgs = userOrgs.filter(org => 
  ['owner', 'admin'].includes(org.role)
)
```

#### Task 3.2: Implement Invitation Sending
**API Integration**:
```javascript
const sendInvitations = async () => {
  try {
    const results = await Promise.allSettled(
      selectedOrgIds.map(orgId => 
        api.orgs.createInvite(orgId, targetUserId, 'member', '')
      )
    )
    
    const succeeded = results.filter(r => r.status === 'fulfilled').length
    const failed = results.filter(r => r.status === 'rejected').length
    
    return { succeeded, failed }
  } catch (error) {
    throw error
  }
}
```

### Phase 4: User Feedback

#### Task 4.1: Loading States
- Show loading spinner while fetching organizations
- Disable buttons during invitation sending
- Show progress indicator on Invite button

#### Task 4.2: Toast Notifications
**Success Case**:
```javascript
setToasts(prevToasts => [...prevToasts, {
  id: Date.now(),
  message: `Invite(s) sent!`,
  type: 'notice'
}])
toastRef.current.show()
```

**Error Case**:
```javascript
setToasts(prevToasts => [...prevToasts, {
  id: Date.now(),
  message: 'Failed to send some invites',
  type: 'error'
}])
toastRef.current.show()
```

#### Task 4.3: Empty States
- Show appropriate message when user has no organizations
- Display helpful text when no organizations allow invites

### Phase 5: Visual Polish

#### Task 5.1: Theme Support
- Use StyleSheetFactory for dark/light mode
- Apply theme-appropriate colors
- Ensure proper contrast ratios

#### Task 5.2: Animations
- Modal slide-in animation
- Checkbox selection animations
- Loading state transitions

### Phase 6: Testing & Validation

#### Task 6.1: Edge Cases
- User with no organizations
- User with no invite permissions
- API failures
- Network connectivity issues

#### Task 6.2: Permission Validation
- Verify user role checking
- Test with different organization roles
- Validate invite permissions

#### Task 6.3: Multi-selection Logic
- Test selecting/deselecting all organizations
- Verify state management accuracy
- Test bulk invitation sending

## Error Handling Strategy

1. **No Organizations**: Show empty state with helpful message
2. **No Permissions**: Hide feature or show disabled state
3. **API Failures**: Show toast with retry option
4. **Partial Success**: Report succeeded and failed counts
5. **Network Issues**: Show offline message with retry

## Performance Considerations

1. **Lazy Loading**: Load organizations only when modal opens
2. **Memoization**: Cache organization list to avoid re-fetching
3. **Batch Processing**: Send all invitations in parallel
4. **Debouncing**: Prevent rapid selection changes

## UI/UX Guidelines

1. **Visual Hierarchy**: Clear organization cards with logos/names
2. **Selection Feedback**: Immediate visual response
3. **Confirmation**: Optional dialog for multiple selections
4. **Progress Indication**: Clear loading states
5. **Success Feedback**: Prominent toast messages

## Code Quality Standards

1. **Type Safety**: Use PropTypes or TypeScript where applicable
2. **Comments**: Document complex logic and API calls
3. **Error Boundaries**: Wrap feature in error boundary
4. **Logging**: Add appropriate console logs for debugging
5. **Linting**: Follow existing ESLint rules

## Deployment Strategy

1. **Feature Flag**: Optional flag to enable/disable feature
2. **Gradual Rollout**: Test with subset of users first
3. **Monitoring**: Track usage and error rates
4. **Rollback Plan**: Easy removal by hiding menu option

## Future Enhancements

1. **Role Selection**: Allow choosing role when inviting
2. **Custom Messages**: Add personal invitation messages
3. **Bulk Actions**: Select all/none buttons
4. **Search/Filter**: Filter organizations by name
5. **Recent Selections**: Remember frequently used organizations

## Dependencies

- Existing components: UserList, GenericUserHolder, ActionSheet
- API endpoints: organizations.createInvite, organizations.getCurrentUserOrgs
- UI components: URadio (for checkbox styling), Toast system
- State management: React Sweet State
- Icons: Font Awesome (faUserPlus)

## Timeline Estimate

- Phase 1: 2-3 hours (Menu integration and modal setup)
- Phase 2: 3-4 hours (Organization list with checkboxes)
- Phase 3: 2-3 hours (API integration)
- Phase 4: 2 hours (User feedback and toasts)
- Phase 5: 2 hours (Visual polish and animations)
- Phase 6: 3 hours (Testing and edge cases)

Total estimate: 14-17 hours of development time

## Success Metrics

1. **Feature Adoption**: Track percentage of eligible users who use the feature
2. **Invitation Success Rate**: Monitor successful vs failed invitations
3. **Bulk Usage**: Track average number of organizations selected
4. **Error Rate**: Monitor API failures and user-facing errors
5. **User Satisfaction**: Gather feedback on the feature's usability