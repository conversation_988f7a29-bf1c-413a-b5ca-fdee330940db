# Organization Invite Feature Implementation Plan (v2)

## Overview
This document outlines the implementation plan for adding an "Invite to organizations" feature to the user profile options in the Unified mobile app. The feature allows users with appropriate permissions to invite other users to multiple organizations simultaneously.

## Feature Requirements
1. Add "Invite to organizations" option to user profile options menu
2. Create a modal/sheet showing organizations where the current user can invite people
3. Display organizations with checkboxes for selection
4. Allow multiple organization selection with "Select All" option
5. Send invites to selected organizations
6. Show detailed success/error feedback using the Toast system
7. Track analytics for feature usage

## Technical Architecture

### Component Structure
```
Profile/Options
└── handleInviteToOrganizations(userId, userName)
    └── OrganizationInviteModal
        ├── Header (title with userName + close)
        ├── SelectionControls (Select All/None)
        ├── OrganizationsList (with checkboxes)
        │   └── UserList (configured for checkbox mode)
        │       └── GenericUserHolder (with checkbox images)
        └── ActionBar (Cancel + Invite buttons)
```

### State Management
```javascript
// LocalStore state shape
{
  isModalVisible: false,
  userOrganizations: [],
  selectedOrgIds: [],
  isLoading: false,
  isSending: false,
  targetUser: {
    id: null,
    name: '',
    account: {}
  },
  inviteResults: {
    succeeded: [],
    failed: [],
    alreadyInvited: []
  }
}
```

## Implementation Phases

### Phase 1: Options Menu Integration (Improved)

#### Task 1.1: Update Profile Options Component
**File**: `components/Profile/Options/index.js`
- Add "Invite to organizations" option at the top of the menu
- Use `faUserPlus` icon from Font Awesome
- Pass complete user profile object to handler
- Add analytics tracking for button tap

```javascript
const handleInviteToOrganizations = () => {
  // Log analytics event
  globalActions.logEvent('invite_to_orgs_initiated', 'Profile/Options', 'tap', 'Profile/Options/index.js', {
    target_user_id: profile.account.id
  })
  
  // Hide options sheet and show invite modal
  actionSheetRef?.current.hide()
  showInviteModal(profile.account.id, profile.display_name, profile)
}
```

#### Task 1.2: Create Modal Component Structure
**File**: `components/Profile/OrganizationInviteModal/index.js`
- Create modal using ActionSheet pattern
- Show target user's name in header: "Invite [username] to an organization"
- Add close button with confirmation if selections exist
- Create footer with disabled state for Invite button when no selections

#### Task 1.3: Create LocalStore for State
**File**: `components/Profile/OrganizationInviteModal/LocalStore/index.js`
- Define comprehensive state structure
- Create actions:
  - `showModal(userId, userName, userProfile)`
  - `hideModal()`
  - `toggleOrganization(orgId)`
  - `selectAllOrganizations()`
  - `deselectAllOrganizations()`
  - `loadUserOrganizations()`
  - `sendInvitations()`
  - `clearResults()`

### Phase 2: Organization List Implementation (Enhanced)

#### Task 2.1: Create Checkbox Images
**Files**: Add to `assets/images/`
- `checkbox-on.png` / `<EMAIL>` / `<EMAIL>`
- `checkbox-off.png` / `<EMAIL>` / `<EMAIL>`
- Use square checkbox design to differentiate from radio buttons

#### Task 2.2: Create Custom Organization List Component
**File**: `components/Profile/OrganizationInviteModal/InviteOrganizationsList/index.js`
- Extend OrganizationsList with checkbox support
- Add "Select All" header control
- Show organization role/permissions
- Disable organizations where user lacks invite permissions
- Mark organizations where user is already invited

```javascript
const checkboxButtonOptions = (org) => {
  const isSelected = selectedOrgIds.includes(org.account.id)
  const canInvite = ['owner', 'admin'].includes(org.role)
  const alreadyInvited = checkExistingInvite(org.account.id, targetUser.id)
  
  return {
    firstButton: {
      title: '',
      customComponent: (
        <Image 
          source={
            alreadyInvited 
              ? require('assets/images/checkbox-disabled.png')
              : isSelected 
                ? require('assets/images/checkbox-on.png')
                : require('assets/images/checkbox-off.png')
          }
          style={styles.checkbox}
        />
      ),
      cb: canInvite && !alreadyInvited ? () => toggleOrganization(org.account.id) : null,
      disabled: !canInvite || alreadyInvited
    }
  }
}
```

### Phase 3: API Integration (Enhanced)

#### Task 3.1: Fetch Organizations with Permissions
**API Enhancement**: Modify response to include permission details
```javascript
const loadUserOrganizations = async () => {
  try {
    const result = await api.orgs.getCurrentUserOrgs()
    // Filter for organizations where user can invite
    const invitableOrgs = result.organizations.filter(org => 
      ['owner', 'admin'].includes(org.role)
    )
    
    // Check for existing invitations
    const orgsWithInviteStatus = await checkExistingInvitations(
      invitableOrgs,
      targetUser.id
    )
    
    setState({ userOrganizations: orgsWithInviteStatus })
  } catch (error) {
    logError('Failed to load organizations', error)
  }
}
```

#### Task 3.2: Implement Batch Invitation with Detailed Results
```javascript
const sendInvitations = async () => {
  setState({ isSending: true })
  
  const results = await Promise.allSettled(
    selectedOrgIds.map(async (orgId) => {
      try {
        await api.orgs.createInvite(orgId, targetUser.id, 'member', '')
        return { orgId, status: 'success' }
      } catch (error) {
        if (error?.info?.status === 409) {
          return { orgId, status: 'already_invited' }
        }
        return { orgId, status: 'failed', error: error.message }
      }
    })
  )
  
  // Categorize results
  const categorized = categorizeResults(results)
  setState({ inviteResults: categorized, isSending: false })
  
  // Show detailed feedback
  showDetailedFeedback(categorized)
}
```

### Phase 4: User Feedback (Enhanced)

#### Task 4.1: Loading States with Progress
- Display progress during batch sending
- Disable interactions appropriately

#### Task 4.2: Detailed Toast Notifications
```javascript
const showDetailedFeedback = (results) => {
  const { succeeded, failed, alreadyInvited } = results
  
  if (succeeded.length > 0 && failed.length === 0) {
    // Complete success
    setToasts(prevToasts => [...prevToasts, {
      id: Date.now(),
      message: `Successfully sent ${succeeded.length} invite${succeeded.length > 1 ? 's' : ''}!`,
      type: 'notice'
    }])
  } else if (succeeded.length > 0 && failed.length > 0) {
    // Partial success
    setToasts(prevToasts => [...prevToasts, {
      id: Date.now(),
      message: `Sent ${succeeded.length} invite${succeeded.length > 1 ? 's' : ''}, ${failed.length} failed`,
      type: 'error'
    }])
  } else if (failed.length > 0) {
    // Complete failure
    setToasts(prevToasts => [...prevToasts, {
      id: Date.now(),
      message: `Failed to send invites. Please try again.`,
      type: 'error'
    }])
  }
  
  toastRef.current.show()
}
```