import core from './core'

class InterestsAPI {
  constructor(http) {
    this.http = http
  }

  getSuggestedInterests() {
    return core.executeGet(this.http, '/interests/suggest?limit=50&offset=0')
  }

  followInterests(ids) {
    return core.executePost(this.http, '/interests/follow', ids)
  }

  unfollowInterests(ids) {
    return core.executePost(this.http, '/interests/unfollow', ids)
  }

  getCurrentUserInterests(ids) {
    return core.executeGet(this.http, '/interests?limit=30', ids)
  }

  createInterests(interests = []) {
    const body = { titles: interests }

    return core.executePost(this.http, '/interests', body)
  }
}

export default InterestsAPI
