import CustomError from './CostumError'

const core = {
  executeGet: async function (http, endpoint, opts = {}) {
    console.log('\x1b[34m%s\x1b[0m', 'GET', endpoint) // Blue color
    const { page, limit, parameters, sort, expand } = opts

    let queryParameters = []

    if (parameters) {
      for (const parameter in parameters) {
        const value = parameters[parameter]

        queryParameters.push(`${parameter}=${value}`)
      }
    }

    if (sort) {
      for (const field in sort) {
        const sortOrder = sort[field]
        queryParameters.push(`sort[${field}]=${sortOrder}`)
      }
    }

    if (expand) {
      for (const field of expand) {
        queryParameters.push(`expand[${field}]`)
      }
    }

    if (page) {
      queryParameters.push(`page=${page}`)
    }

    if (limit) {
      queryParameters.push(`limit=${limit}`)
    }

    queryParameters = queryParameters.join('&')

    const url = queryParameters ? `${endpoint}?${queryParameters}` : endpoint

    try {
      const { data } = await http.get(url)
      return data
    } catch (error) {
      if (error.response) {
        const { status, data } = error.response
        console.log('LAST CALL STATUS', status)
        let errorName

        if (status === 400) {
          errorName = 'USER_ERROR'
        } else if (status === 401) {
          errorName = 'UNAUTHORIZED_ERROR'
        } else if (status === 403) {
          errorName = 'FORBIDDEN_ERROR'
        } else if (status === 404) {
          errorName = 'NOT_FOUND_ERROR'
        } else if (status >= 500) {
          errorName = 'INTERNAL_ERROR'
        }
        const seed = {
          name: errorName,
          info: { url, status, responseBody: data },
        }
        // console.log('ERROR SEED GET', JSON.stringify(error))
        throw new CustomError(seed, data?.errors?.join(', '))
      } else {
        const seed = {
          name: 'INTERNAL_ERROR',
          cause: error,
          info: { url: endpoint },
        }
        throw new CustomError(seed, 'Oops')
      }
    }
  },

  executePost: async function (http, endpoint, body, options = {}) {
    console.log('\x1b[32m%s\x1b[0m', 'POST', endpoint) // Green color
    try {
      const { data } = await http.post(endpoint, body, options)
      return data
    } catch (error) {
      console.log('RAW ERROR', error)
      if (error.response) {
        const { status = 0, data = {} } = error?.response
        console.log('LAST CALL STATUS', status)
        let errorName

        if (status === 400) {
          errorName = 'USER_ERROR'
        } else if (status === 401) {
          errorName = 'UNAUTHORIZED_ERROR'
        } else if (status === 403) {
          errorName = 'FORBIDDEN_ERROR'
        } else if (status === 404) {
          errorName = 'NOT_FOUND_ERROR'
        } else if (status >= 500) {
          errorName = 'INTERNAL_ERROR'
        }
        const seed = {
          name: errorName,
          info: { url: endpoint, status, requestBody: body, responseBody: data },
        }
        console.log('ERROR SEED POST', JSON.stringify(seed))
        throw new CustomError(seed, data?.errors?.join(', '))
      } else {
        const seed = {
          name: 'INTERNAL_ERROR',
          cause: error,
          info: { url: endpoint, requestBody: body },
        }
        console.log('ERROR SEED', seed)
        throw new CustomError(seed, 'Oops')
      }
    }
  },

  executePatch: async function (http, endpoint, body) {
    console.log('\x1b[33m%s\x1b[0m', 'PATCH', endpoint) // Yellow color
    try {
      const { data } = await http.patch(endpoint, body)
      return data
    } catch (error) {
      if (error.response) {
        const { status, data } = error.response
        console.log('LAST CALL STATUS', status)
        let errorName

        if (status === 400) {
          errorName = 'USER_ERROR'
        } else if (status === 401) {
          errorName = 'UNAUTHORIZED_ERROR'
        } else if (status === 403) {
          errorName = 'FORBIDDEN_ERROR'
        } else if (status === 404) {
          errorName = 'NOT_FOUND_ERROR'
        } else if (status >= 500) {
          errorName = 'INTERNAL_ERROR'
        }
        const seed = {
          name: errorName,
          info: { url: endpoint, status, requestBody: body, responseBody: data },
        }
        throw new CustomError(seed, data?.errors?.join(', '))
      } else {
        const seed = {
          name: 'INTERNAL_ERROR',
          cause: error,
          info: { url: endpoint, requestBody: body },
        }
        throw new CustomError(seed, 'Oops')
      }
    }
  },

  executePut: async function (http, endpoint, body) {
    console.log('\x1b[35m%s\x1b[0m', 'PUT', endpoint) // Magenta color
    try {
      const { data } = await http.put(endpoint, body)
      return data
    } catch (error) {
      if (error.response) {
        const { status, data } = error.response
        console.log('LAST CALL STATUS', status)
        let errorName

        if (status === 400) {
          errorName = 'USER_ERROR'
        } else if (status === 401) {
          errorName = 'UNAUTHORIZED_ERROR'
        } else if (status === 403) {
          errorName = 'FORBIDDEN_ERROR'
        } else if (status === 404) {
          errorName = 'NOT_FOUND_ERROR'
        } else if (status >= 500) {
          errorName = 'INTERNAL_ERROR'
        }
        const seed = {
          name: errorName,
          info: { url: endpoint, status, requestBody: body, responseBody: data },
        }
        throw new CustomError(seed, data?.errors?.join(', '))
      } else {
        const seed = {
          name: 'INTERNAL_ERROR_2',
          cause: error,
          info: { url: endpoint, requestBody: body },
        }
        throw new CustomError(seed, 'Oops')
      }
    }
  },

  executeDelete: async function (http, endpoint, body) {
    console.log('\x1b[31m%s\x1b[0m', 'DELETE', endpoint) // Red color
    try {
      const { data } = await http.delete(endpoint, body)
      return data
    } catch (error) {
      if (error.response) {
        const { status, data } = error.response
        console.log('LAST CALL STATUS', status)
        let errorName

        if (status === 400) {
          errorName = 'USER_ERROR'
        } else if (status === 401) {
          errorName = 'UNAUTHORIZED_ERROR'
        } else if (status === 403) {
          errorName = 'FORBIDDEN_ERROR'
        } else if (status === 404) {
          errorName = 'NOT_FOUND_ERROR'
        } else if (status >= 500) {
          errorName = 'INTERNAL_ERROR'
        }
        const seed = { name: errorName, info: { url: endpoint, status, requestBody: body, responseBody: data } }
        throw new CustomError(seed, data?.errors?.join(', '))
      } else {
        const seed = {
          name: 'INTERNAL_ERROR',
          cause: error,
          info: { url: endpoint, requestBody: body },
        }
        throw new CustomError(seed, 'Oops')
      }
    }
  },
}

export default core
