import core from './core'

class FeedAPI {
  constructor(http) {
    this.http = http
  }

  getFeed(limit = 10, cursor = '') {
    const uri = '/feed'
    return core.executeGet(this.http, uri, { parameters: { limit, cursor, feed_type: 'following' } })
  }

  getPublicFeed(limit = 10, cursor = '') {
    const uri = '/feed'
    return core.executeGet(this.http, uri, { parameters: { limit, cursor } })
  }
}

export default FeedAPI
