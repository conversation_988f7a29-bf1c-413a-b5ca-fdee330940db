import core from './core'

class ReportAPI {
  constructor(http) {
    this.http = http
  }

  createReport(reason, comment, oid, type) {
    const uri = '/report'
    const body = {
      reason,
      comment,
      object_id: oid,
      object_kind: type === 'profile' ? 'user' : type,
    }
    if (body.comment.trim() === '' || body.comment.trim().length < 5) {
      delete body.comment
    }
    return core.executePost(this.http, uri, body)
  }
}

export default ReportAPI
