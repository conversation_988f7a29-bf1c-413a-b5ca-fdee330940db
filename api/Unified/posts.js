import core from './core'

class PostsAPI {
  constructor(http) {
    this.http = http
  }

  createPost(title, description, assets, father, orgId) {
    const body = { title, description }
    if (assets) {
      body.attachments = assets
    }
    if (father) {
      body.parent_post_id = father
    }
    if (orgId) {
      body.on_behalf_of_account_id = orgId
    }
    console.log('POST BODY', body)
    return core.executePost(this.http, '/posts', body)
  }

  getPost(postId) {
    const uri = `/posts/${postId}`
    return core.executeGet(this.http, uri)
  }

  deletePost(postId) {
    const uri = `/posts/${postId}`
    return core.executeDelete(this.http, uri)
  }

  getComments(postId, highlight = false) {
    let uri = `/posts/${postId}/comments`
    if (highlight) {
      uri += `?highlighted_comment_id=${highlight}`
    }
    return core.executeGet(this.http, uri)
  }

  //pid => post id
  getChildPosts(pid, limit = 10, offset = 0, favoriteChild = null) {
    const uri = `/posts/${pid}/child_posts`
    const parameters = { limit, offset }
    if (favoriteChild) {
      parameters.highlighted_post_id = favoriteChild.id
    }
    return core.executeGet(this.http, uri, { parameters })
  }

  getPostByComment(commentId) {
    const uri = `/posts/comment/${commentId}`
    return core.executeGet(this.http, uri)
  }

  getUserPosts(userId, cursor = '') {
    const uri = `/posts/user/${userId}`
    return core.executeGet(this.http, uri, { parameters: { limit: 10, cursor } })
  }

  getRelatedList(pid, type) {
    const uris = {
      likes: `/posts/${pid}/likes/users`,
      comments: `/posts/${pid}/comments/users`,
      completions: `/posts/${pid}/action_completions/users`,
    }
    return core.executeGet(this.http, uris[type])
  }

  getAssociatedData(postId, actionId, refresh = false) {
    let uri = `/posts/${postId}/actions/${actionId}`
    if (refresh) {
      uri = uri + '?refresh_contacts=true'
    }
    return core.executeGet(this.http, uri)
  }

  getActionCompletors(pid) {
    const uri = `/posts/${pid}/action_completions/users`
    return core.executeGet(this.http, uri)
  }

  getFeaturedProfiles(pid, limit, offset, filter = null) {
    let uri = `/posts/${pid}/featured_profiles?limit=${limit}&offset=${offset}`
    if (filter) {
      uri += `&filter=${filter}`
    }
    return core.executeGet(this.http, uri)
  }

  //cid->child id
  getParentPost(cid) {
    const uri = `/posts/${cid}/parent_post`
    return core.executeGet(this.http, uri)
  }

  searchPosts(searchTerm, limit = 10, offset = 0) {
    const body = { search_term: searchTerm, limit: limit, offset: offset }
    return core.executePost(this.http, '/posts/search', body)
  }
}

export default PostsAPI
