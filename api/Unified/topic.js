import core from './core'

class TopicAPI {
  constructor(http) {
    this.http = http
  }

  getTrendingTopics(limit = 4) {
    return core.executePost(this.http, '/topic/trending', { limit: limit })
  }

  getTopicResults(topicId, post_limit = 10, post_offset = 0, account_limit = 10, account_offset = 0) {
    return core.executePost(this.http, `/topic/${topicId}`, { topic_id: topicId, post_limit: post_limit, post_offset: post_offset, account_limit: account_limit, account_offset: account_offset })
  }
}

export default TopicAPI
