import core from './core'

class FollowersAPI {
  constructor(http) {
    this.http = http
  }

  //aid -> account id
  followUser(aid) {
    const uri = `/followers/account/${aid}/follow`
    return core.executePost(this.http, uri)
  }

  unfollowUser(aid) {
    const uri = `/followers/account/${aid}/unfollow`
    return core.executePost(this.http, uri)
  }

  getSuggestedFollowers() {
    const uri = '/followers/suggested_followers?limit=8'
    return core.executeGet(this.http, uri)
  }

  getFollowing() {
    const uri = '/followers/following'
    return core.executeGet(this.http, uri)
  }

  getFollowers() {
    const uri = '/followers/followers'
    return core.executeGet(this.http, uri)
  }

  getFollowingWithId(aid) {
    const uri = `/followers/account/${aid}/following`
    return core.executeGet(this.http, uri)
  }

  getFollowersWithId(aid) {
    const uri = `/followers/account/${aid}/followers`
    return core.executeGet(this.http, uri)
  }
}

export default FollowersAPI
