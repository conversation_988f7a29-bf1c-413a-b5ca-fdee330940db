import axios from 'axios'
import { Platform } from 'react-native'
import core from './core'

class AsesstsAPI {
  constructor(http) {
    this.http = http
  }

  registerAssetNoAuth(kind, fileName, token, height, width, description) {
    const body = {
      kind,
      file_name: fileName,
      registration_token_id: token,
    }
    if (height) {
      body.height = height
    }
    if (width) {
      body.width = width
    }
    if (description) {
      body.description = description
    }
    return core.executePost(this.http, '/assets/register_asset_by_registration_token', body)
  }

  registerAssetWithWorkflowId(kind, fileName, wfid, height, width, description) {
    const body = {
      kind,
      file_name: fileName,
      workflow_id: wfid,
    }
    if (height) {
      body.height = height
    }
    if (width) {
      body.width = width
    }
    if (description) {
      body.description = description
    }
    return core.executePost(this.http, '/assets/register_asset_by_workflow_id', body)
  }

  registerAsset(kind, fileName, height, width, description) {
    const body = {
      kind,
      file_name: fileName,
    }
    if (height) {
      body.height = height
    }
    if (width) {
      body.width = width
    }
    if (description) {
      body.description = description
    }
    return core.executePost(this.http, '/assets', body)
  }

  // get asset by id
  getAssetById(id) {
    return core.executeGet(this.http, `/assets/${id}`)
  }

  uploadToS3(assetInfo, photo, contentType) {
    const data = new FormData()

    Object.keys(assetInfo.upload_fields).forEach(field => {
      data.append(field, assetInfo.upload_fields[field])
    })

    data.append('file', {
      name: photo.fileName,
      type: photo.type,
      uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
    })

    const options = {
      headers: { 'Content-Type': contentType },
      onUploadProgress: function (progressEvent) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        const loadedMB = (progressEvent.loaded / 1048576).toFixed(2) // Convert bytes to megabytes
        const totalMB = (progressEvent.total / 1048576).toFixed(2)
        console.log(`Uploaded: ${percentCompleted}% (${loadedMB} MB of ${totalMB} MB)`)
      },
    }

    // console.log('SENDING TO AWS', assetInfo.upload_uri, data, options)
    return axios.post(assetInfo.upload_uri, data, options)
  }

  markUploaded(id) {
    const uri = `/assets/${id}/finish_upload_by_user`
    return core.executePatch(this.http, uri)
  }
}

export default AsesstsAPI
