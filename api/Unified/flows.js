import core from './core'
import { Platform } from 'react-native'

class FlowsAPI {
  constructor(http) {
    this.http = http
  }

  createFlow(kind, actionInstance) {
    const metadata = {
      kind: 'mobile',
      version: 'tbd',
      os: Platform.OS,
      os_version: 'tbd',
    }

    const body = {
      kind,
      metadata,
      action_instance: actionInstance,
    }
    return core.executePut(this.http, '/flows', body)
  }

  updateFlow(id, state, transition, data) {
    const body = {}

    if (state) {
      body.state = state
    }
    if (transition) {
      body.transition = transition
    }
    if (data) {
      body.data = data
    }
    console.log('UF', JSON.stringify(body))
    return core.executePost(this.http, `/flows/${id}`, body)
  }
}

export default FlowsAPI
