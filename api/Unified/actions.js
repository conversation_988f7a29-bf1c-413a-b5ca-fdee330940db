import core from './core'

class ActionsAPI {
  constructor(http) {
    this.http = http
  }

  createAction(title, description, startDate, templateId = 1, contactFilters, targetedReps, subject, emailBody, orgId) {
    const body = {
      title,
      description,
      start_date: startDate,
      template_id: templateId,
    }
    if (contactFilters) {
      body.contact_filters = contactFilters
    }
    if (targetedReps) {
      body.targeted_reps = targetedReps
    }
    if (subject) {
      body.email_subject = subject
    }
    if (emailBody) {
      body.email_body = emailBody
    }
    if (orgId) {
      body.on_behalf_of_account_id = orgId
    }

    return core.executePost(this.http, '/actions', body)
  }

  completeAction(actionId) {
    return core.executePost(this.http, `/actions/${actionId}/complete`)
  }

  getActions(search = '', kind = '', limit = 10, cursor = '') {
    const uri = '/actions'
    return core.executeGet(this.http, uri, { parameters: { search, kind, limit, cursor } })
  }

  getAction(id) {
    const uri = `/actions/${id}`
    return core.executeGet(this.http, uri)
  }

  getActionTargets(id) {
    const uri = `/actions/${id}/with_targets`
    return core.executeGet(this.http, uri)
  }

  getActionsByUID(uid, cursor = '', limit = 10, iabom = false, iaboms = false) {
    const uri = `/actions/user/${uid}`
    const parameters = { limit, cursor }
    if (iabom) {
      parameters.include_actions_by_org_membership = true
    }
    if (iaboms) {
      parameters.include_actions_by_org_members = true
    }
    console.log('PARAMS', uri, parameters)
    return core.executeGet(this.http, uri, { parameters })
  }

  getActionsCompletors(id) {
    const uri = `/actions/${id}/completors`
    return core.executeGet(this.http, uri)
  }

  getCompletedActions(aid, limit = 10, offset = 0) {
    const uri = `/actions/completed/user/${aid}`
    return core.executeGet(this.http, uri, { parameters: { limit, offset } })
  }
}

export default ActionsAPI
