import axios from 'axios'
import core from './core'
import FeedAPI from './feed'
import ChatAPI from './chat'
import JobsAPI from './jobs'
import TipsAPI from './tips'
import PostsAPI from './posts'
import FlowsAPI from './flows'
import UsersAPI from './users'
import LikesAPI from './likes'
import LinksAPI from './links'
import TopicAPI from './topic'
import BlockAPI from './block'
import InviteAPI from './invite'
import SearchAPI from './search'
import ReportAPI from './report'
import LocaleAPI from './locale'
import SharesAPI from './shares'
import AsesstsAPI from './assets'
import LookUpsAPI from './lookups'
import ActionsAPI from './actions'
import LoggingAPI from './logging'
import ExploreAPI from './explore'
import CommentsAPI from './comments'
import ContactsAPI from './contacts'
import AccountsAPI from './accounts'
import InterestsAPI from './interests'
import FollowersAPI from './followers'
import ValidationAPI from './validation'
import { API_HOST } from '../../constants'
import SearchHistoryAPI from './searchHistory'
import OrganizationsAPI from './organizations'
import NotificationsAPI from './notifications'
import ActionIntanceAPI from './actionInstance'
import RepresentativesAPI from './representatives'
import AccountsSettingsAPI from './accountSettings'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Platform } from 'react-native'

class UNIFIEDAPI {
  constructor(authToken, { newSessionId, appVersion }) {
    this._authToken = authToken || null
    this.init(authToken, newSessionId, appVersion)
  }

  async getHost() {
    const host = await AsyncStorage.getItem('APIURL')
    return host
  }

  async init(authToken, newSessionId, appVersion) {
    this._authToken = authToken || null
    const host = await this.getHost()

    console.log('HOST', host, API_HOST)

    const axiosOpts = {
      baseURL: host ? host : API_HOST,
      headers: {
      },
    }

    if (this._authToken) {
      axiosOpts.headers = {
        Authorization: `Bearer ${this._authToken}`,
      }
    }

    this.http = axios.create(axiosOpts)
    this.http.defaults.headers.common['session-id'] = newSessionId
    this.http.defaults.headers.common['app-version'] = appVersion
    this.http.defaults.headers.common['client-type'] = Platform.select({ios:'ios',android:'android'})

    this.feed = new FeedAPI(this.http)
    this.jobs = new JobsAPI(this.http)
    this.chat = new ChatAPI(this.http)
    this.tips = new TipsAPI(this.http)
    this.users = new UsersAPI(this.http)
    this.flows = new FlowsAPI(this.http)
    this.block = new BlockAPI(this.http)
    this.posts = new PostsAPI(this.http)
    this.likes = new LikesAPI(this.http)
    this.links = new LinksAPI(this.http)
    this.topic = new TopicAPI(this.http)
    this.locale = new LocaleAPI(this.http)
    this.search = new SearchAPI(this.http)
    this.invite = new InviteAPI(this.http)
    this.report = new ReportAPI(this.http)
    this.assets = new AsesstsAPI(this.http)
    this.lookups = new LookUpsAPI(this.http)
    this.actions = new ActionsAPI(this.http)
    this.explore = new ExploreAPI(this.http)
    this.logging = new LoggingAPI(this.http)
    this.accounts = new AccountsAPI(this.http)
    this.contacts = new ContactsAPI(this.http)
    this.comments = new CommentsAPI(this.http)
    this.orgs = new OrganizationsAPI(this.http)
    this.interests = new InterestsAPI(this.http)
    this.followers = new FollowersAPI(this.http)
    this.history = new SearchHistoryAPI(this.http)
    this.validation = new ValidationAPI(this.http)
    this.notifications = new NotificationsAPI(this.http)
    this.actionInstance = new ActionIntanceAPI(this.http)
    this.representatives = new RepresentativesAPI(this.http)
    this.accountSettings = new AccountsSettingsAPI(this.http)
    this.shares = new SharesAPI(this.http)
  }

  set authToken(authToken) {
    this._authToken = authToken
    this.init(authToken)
  }

  getAuthToken() {
    return this._authToken
  }

  getAxiosInstance() {
    return this.http
  }

  auth(username, password) {
    const bodyFormData = new FormData()
    bodyFormData.append('username', username)
    bodyFormData.append('password', password)
    return core.executePost(this.http, '/auth/login', bodyFormData)
  }

  startUsernameRegistration(email = '', phone = '', inviteCode = '') {
    if (email.trim()) {
      const body = inviteCode.trim() ? { email, invite_code: inviteCode } : { email }
      return core.executePost(this.http, '/auth/start-registration', body)
    } else if (phone.trim()) {
      const body = inviteCode.trim() ? { phone_number: phone, invite_code: inviteCode } : { phone_number: phone }
      return core.executePost(this.http, '/auth/start-registration', body)
    }
  }

  getRegistrationTokenFromSMSValidationCode(code, number) {
    const body = { sms_code: code, phone_number: number }
    console.log('BODY-SMS', '/auth/token_from_sms', body)
    return core.executePost(this.http, '/auth/token_from_sms', body)
  }

  registerFromToken(token, fname, lname, password) {
    return core.executePost(this.http, '/auth/register-from-token', {
      token,
      password,
      first_name: fname,
      last_name: lname,
    })
  }


  //New Auth 2025

  startDirectRegistration(email = '', phone = '', inviteCode = '') {
    if (email.trim()) {
      const body =  { email, invite_code: inviteCode }
      return core.executePost(this.http, '/auth/start-direct-registration', body)
    } else if (phone.trim()) {
      const body =  { phone_number: phone, invite_code: inviteCode }
      return core.executePost(this.http, '/auth/start-direct-registration', body)
    }
  }

  continueDirectRegistration(workflowId, captcha, fname, lname, password, assetId) {
    const body = {
      workflow_id: workflowId,
      given_name: fname,
      family_name: lname,
      password,
      captcha_verification: captcha,
    }
    if (assetId) {
      body.asset_id = assetId
    }
    return core.executePost(this.http, '/auth/continue-direct-registration', body)
  }

  sendDirectRegistrationConfirmation() {
    return core.executePost(this.http, '/auth/send-direct-registration-confirmation', {})
  }

  confirmDirectRegistration(token) {
    const body = {
      reg_token:token,
    }
    return core.executePost(this.http, '/auth/confirm-direct-registration', body)
  }

  //End New Auth 2025
  checkTokenValidity() {
    return core.executePost(this.http, '/auth/check-token')
  }

  requestPasswordReset(username) {
    return core.executePost(this.http, '/auth/start_reset_password', { username })
  }

  resetPassword(token, new_password) {
    return core.executePost(this.http, '/auth/reset_password', { new_password, token })
  }

  changePassword(current_password, new_password, confirm_password) {
    return core.executePost(this.http, '/auth/change_password', { current_password, new_password, confirm_password })
  }
}

export default UNIFIEDAPI
