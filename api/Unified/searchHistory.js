import core from './core'

class SearchHistoryAPI {
  constructor(http) {
    this.http = http
  }

  getTrendingHistories(searchKind = 'post', limit = 20) {
    const body = { search_kind: searchKind, limit: limit }
    return core.executePost(this.http, '/search_history/trending', body)
  }

  getHistory(limit = 15) {
    return core.executePost(this.http, '/search_history/get_by_account', { limit: limit })
  }
}

export default SearchHistoryAPI
