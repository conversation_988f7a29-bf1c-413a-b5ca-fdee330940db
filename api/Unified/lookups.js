import core from './core'

class LookUpsAPI {
  constructor(http) {
    this.http = http
  }

  getUsersByName(name) {
    const uri = `/search/name/${name}`
    return core.executeGet(this.http, uri)
  }

  listAreaCodes(code) {
    const uri = '/lookups/area_codes'
    return core.executeGet(this.http, uri, { parameters: { code, limit: 7 } })
  }

  listVoterRegStates() {
    const uri = '/lookups/voter_reg_states'
    return core.executeGet(this.http, uri)
  }

  checkVoterReg(contactID, postID, actionID, firstName = ' ', lastName = ' ', address = ' ', city = ' ', state = ' ', zipcode = ' ', dob) {
    const uri = '/lookups/voter_reg'
    const body = {
      city,
      state,
      post_id: postID,
      zip_code: zipcode,
      last_name: lastName,
      action_id: actionID,
      first_name: firstName,
      contact_id: contactID,
      street_address: address,
    }

    if (dob) {
      let { day, month, year } = dob
      day = day === '' ? 0 : day
      month = month === '' ? 0 : month
      year = year === '' ? 0 : year
      body.date_of_birth = { day, month, year }
    } else {
      body.date_of_birth = {
        year: 0,
        month: 0,
        day: 0,
      }
    }
    // console.log('VOTER REG BODY', dob, JSON.stringify(body, null, 3))
    return core.executePost(this.http, uri, body)
  }
}

export default LookUpsAPI
