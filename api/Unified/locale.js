import core from './core'

class LocaleAPI {
  constructor(http) {
    this.http = http
  }

  getLocale() {
    const uri = '/locale/user'

    return core.executeGet(this.http, uri)
  }

  setLocaleQuickly(zip) {
    const uri = '/locale/user/quick_set_locale'

    return core.executePost(this.http, uri, { zip })
  }

  getMap() {
    const uri = '/locale/user/map/base64'

    return core.executePost(this.http, uri, {
      size: [420, 190],
      zoom: 13,
    })
  }
}

export default LocaleAPI
