import core from './core'
import { Platform } from 'react-native'

class ContactsAPI {
  constructor(http) {
    this.http = http
  }

  getContactsByFilter(areaCodes = [], status = []) {
    let uri = '/contacts'
    if (areaCodes.length > 0) {
      uri += `?filter=area_code=${areaCodes.join(',')}`
    } else if (status.length > 0) {
      uri += `?filter=voter_reg_status=${status.join(',')}`
    }
    return core.executeGet(this.http, uri)
  }

  matchContactVoterReg(contactId, voterId, givenName = ' ', familyName = ' ', street = ' ', city = ' ', state = ' ', zipcode = ' ', dob) {
    const body = {
      city,
      state,
      street: street,
      postal_code: zipcode,
      voter_reg_id: voterId,
      given_name: givenName,
      family_name: familyName,
    }
    if (dob) {
      let { day, month, year } = dob
      day = day === '' ? 0 : day
      month = month === '' ? 0 : month
      year = year === '' ? 0 : year
      body.date_of_birth = { day, month, year }
    } else {
      body.date_of_birth = {
        year: 0,
        month: 0,
        day: 0,
      }
    }
    return core.executePut(this.http, `/contacts/${contactId}/voter_reg`, body)
  }

  syncContacts(batchId, totalContacts, contacts) {
    const client = {
      kind: 'mobile',
      version: 'tbd',
      os: Platform.OS,
      os_version: 'tbd',
    }

    const body = {
      client,
      contacts,
      batch_id: batchId,
      total_contacts: totalContacts,
      source: `${Platform.OS}_contacts`,
    }
    // console.log('CONTACT SYNC BODY', JSON.stringify(body))
    return core.executePut(this.http, '/contacts', body)
  }

  markContacted(contactId, postId, actionId, method) {
    const uri = `/contacts/${contactId}/voter_reg_contacted`

    const body = {
      post_id: postId,
      action_id: actionId,
      contact_method: method,
    }

    // console.log('SEND', contactId, JSON.stringify(body, null, 3))
    return core.executePut(this.http, uri, body)
  }

  deleteContacts() {
    const uri = '/contacts/delete_contacts'
    return core.executePost(this.http, uri, { confirm: true })
  }

  getContacts(offset) {
    const uri = '/contacts'
    const body = {
      filters: {},
      sort: ['created_at', 1],
      limit: 200,
      offset,
    }
    return core.executePost(this.http, uri, body)
  }
}

export default ContactsAPI
