import core from './core'

class OrganizationsAPI {
  constructor(http) {
    this.http = http
  }

  createOrg(displayName, organizationType, bio, profilePicAssetId, profileBackgroundPicAssetId, links = [], domain) {
    const uri = '/organizations/'
    const body = {
      display_name: displayName,
      organization_type: organizationType,
      bio: bio,
      profile_pic_id: profilePicAssetId || null,
      links,
      domain,
    }

    if (profileBackgroundPicAssetId) {
      body.profile_page_pic_id = profileBackgroundPicAssetId
    }

    console.log('ORG BODY', body)
    return core.executePost(this.http, uri, body)
  }

  getOrg(orgId) {
    const uri = `/organizations/${orgId}`
    return core.executeGet(this.http, uri)
  }

  updateOrg(orgId, displayName, organizationType, bio, profilePicAssetId, profileBackgroundPicAssetId, links = [], domain) {
    const uri = `/organizations/${orgId}`

    const body = {
      display_name: displayName,
      organization_type: organizationType,
      bio: bio,
      profile_pic_id: profilePicAssetId,
      links,
      domain,
    }

    if (profileBackgroundPicAssetId) {
      body.profile_page_pic_id = profileBackgroundPicAssetId
    }

    console.log('ORG BODY', body)

    return core.executePost(this.http, uri, body)
  }
  getOrgMembers(orgId, limit = 30, offset = 0) {
    const uri = `/organizations/${orgId}/members?limit=${limit}&offset=${offset}`
    return core.executeGet(this.http, uri)
  }

  getCurrentUserOrgs() {
    const uri = '/organizations/account/organization'
    return core.executeGet(this.http, uri)
  }

  getUserOrgs(aid) {
    const uri = `/organizations/account/${aid}/organization`
    return core.executeGet(this.http, uri)
  }

  // Requires orgId
  getOrganizationImpact(orgId) {
    const uri = `/organizations/impact_report/${orgId}`
    return core.executeGet(this.http, uri)
  }

  // Requires orgId
  requestToJoin(orgId) {
    const uri = `/organizations/join/organization/${orgId}/request`
    return core.executePost(this.http, uri, { note: 'mobile_created' })
  }

  // Requires orgId and optional limit and offset
  getRequestToJoin(orgId, limit = 30, offset = 0) {
    const uri = `/organizations/join/organization/${orgId}/requests?limit=${limit}&offset=${offset}`
    return core.executeGet(this.http, uri)
  }

  // Requires orgId
  listJoinRequests(orgId) {
    const uri = `/organizations/join/organization/${orgId}/requests`
    return core.executeGet(this.http, uri)
  }

  // Requires orgId, jrId
  // jrId is the join request id
  approveJoinRequest(orgId, jrId) {
    const uri = `/organizations/join/organization/${orgId}/request/${jrId}/approve`
    return core.executePost(this.http, uri)
  }

  // Requires orgId, jrId
  // jrId is the join request id
  denyJoinRequest(orgId, jrId) {
    const uri = `/organizations/join/organization/${orgId}/request/${jrId}/deny`
    return core.executePost(this.http, uri)
  }

  // Requires inviteId
  acceptInvite(inviteId) {
    const uri = `/organizations/invite/${inviteId}/accept`
    return core.executePost(this.http, uri)
  }

  // Requires inviteId
  declineInvite(inviteId) {
    const uri = `/organizations/invite/${inviteId}/decline`
    return core.executePost(this.http, uri)
  }

  // Lists invites for the current user
  listInvites() {
    const uri = '/organizations/invite'
    return core.executeGet(this.http, uri)
  }

  // Requires orgId, aid
  // aid is the account id of the user to kick
  kickMember(orgId, aid) {
    const uri = `/organizations/${orgId}/member/${aid}/kick`
    return core.executePost(this.http, uri)
  }

  // Requires orgId
  leaveOrg(orgId) {
    const uri = `/organizations/${orgId}/leave`
    return core.executePost(this.http, uri)
  }

  // Requires orgId, aid
  // aid is the account id of the user to invite
  createInvite(orgId, aid, role = 'member', title = '') {
    const uri = `/organizations/invite/organization/${orgId}/account/${aid}`
    return core.executePost(this.http, uri, { note: 'mobile_created', role, title })
  }

  // Manage user
  manageUser(orgId, targetAccountId, role, title) {
    const uri = `/organizations/${orgId}/member/${targetAccountId}/manage`
    return core.executePost(this.http, uri, { role, title })
  }

  // Requires orgId
  getOutgoingInvites(orgId, limit = 1000, offset = 0) {
    const uri = `/organizations/invite/${orgId}/outgoing/account_ids?limit=${limit}&offset=${offset}`
    return core.executeGet(this.http, uri)
  }

  // Get all organization types
  getOrganizationTypes() {
    const uri = '/organizations/types/all'
    return core.executeGet(this.http, uri)
  }
}

export default OrganizationsAPI
