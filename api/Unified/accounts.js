import core from './core'

class AccountsAPI {
  constructor(http) {
    this.http = http
  }

  getMe() {
    const uri = '/accounts/me'
    return core.executeGet(this.http, uri)
  }

  deleteUser() {
    const uri = '/accounts/delete'
    return core.executePost(this.http, uri, { confirm: true })
  }

  postDeviceInfo(body) {
    const uri = '/accounts/device_info'
    return core.executePost(this.http, uri, { ...body, await_store_session: true })
  }

  postDeviceInfoWithSession(body) {
    const uri = '/accounts/set_device_info'
    return core.executePost(this.http, uri, { ...body })
  }
}

export default AccountsAPI
