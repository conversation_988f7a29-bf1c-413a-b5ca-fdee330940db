import core from './core'

class LoggingAPI {
  constructor(http) {
    this.http = http
  }

  logEvent(label = '', view = '', input_type = '', component = '', metadata) {
    const uri = '/logging/user_event'
    const body = {
      user_event: {
        view,
        label,
        component,
        input_type,
        timestamp: new Date().toISOString(),
        metadata,
      },
    }
    return core.executePost(this.http, uri, body)
  }

  logEventNoAuth(label = '', view = '', input_type = '', component = '', metadata) {
    const uri = '/logging/user_event_unauth'
    const body = {
      user_event: {
        view,
        label,
        component,
        input_type,
        timestamp: new Date().toISOString(),
        metadata,
      },
    }
    return core.executePost(this.http, uri, body)
  }

  logDeviceInfoWithSessionNoAuth(body) {
    const uri = '/logging/device_info'
    return core.executePost(this.http, uri, { ...body })
  }
}

export default LoggingAPI
