import core from './core'

class ExploreAPI {
  constructor(http) {
    this.http = http
  }

  exploreUsers(limit = 25, offset = 0, search = null, following_interests = [], options = false, includeOrgs = false, excludeOrgMembers = []) {
    const uri = '/explore/users'
    const params = {
      limit,
      offset,
      resource_kind: 'account',
      filter: {
        exclude_following: false,
      },
      options: {
        proximity_only: false,
      },
    }

    if (following_interests.length > 0) {
      params.filter.following_interests = following_interests
    }

    if (search && search !== undefined && search !== null && search !== '') {
      params.filter.name = search
    }

    if (options) {
      params.options.proximity_only = true
    }

    params.filter.include_organizations = includeOrgs

    if (excludeOrgMembers.length > 0) {
      params.filter.exclude_members_from_organizations = excludeOrgMembers
    }

    console.log('EXPLORE USERS REQUEST', params)

    return core.executePost(this.http, uri, params)
  }
}

export default ExploreAPI
