import core from './core'

class RepresentativesAPI {
  constructor(http) {
    this.http = http
  }

  getOfficialsById(id) {
    const uri = `/public_offices/officials/byId/${id}`
    return core.executeGet(this.http, uri)
  }

  getOfficials(filter = '') {
    const uri = '/public_offices/officials'
    return core.executeGet(this.http, uri, { parameters: { filter } })
  }

  getOffices(filter = '') {
    const uri = '/public_offices/offices'
    return core.executeGet(this.http, uri, { parameters: { filter } })
  }

  getRepsForAccount(aid) {
    const uri = `/public_offices/user_officials/user/${aid}`
    return core.executeGet(this.http, uri)
  }
}

export default RepresentativesAPI
