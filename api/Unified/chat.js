import core from './core'

class ChatAPI {
  constructor(http) {
    this.http = http
  }

  //Create Room Invite Link
  createRoomInviteLink(roomId) {
    return core.executePost(this.http, `/chat/room/${roomId}/create_invite_link`, {})
  }

  //Accept Room Invite Link
  acceptRoomInviteLink(token) {
    return core.executePost(this.http, `/chat/room/accept_invite_link/${token}`, {})
  }

  // Create a chat room
  createRoom(name, topic, inviteAccountIds, isDirect, roomImageUrl, organizationId, visibility = 'public') {
    const body = { name, topic, invite_account_ids: inviteAccountIds, is_direct: isDirect }
    if (roomImageUrl) {
      body.room_image_url = roomImageUrl
    }

    // If organizationId is provided, it is a channel
    if (organizationId) {
      body.organization_id = organizationId
      body.visibility = visibility
    }

    console.log('CREATE ROOM', JSON.stringify(body))
    return core.executePost(this.http, '/chat/room/', body)
  }

  // Edits a room's attribute.
  updateRoomAttribute(roomId, eventType, content) {
    const body = { event_type: eventType, state_key: '', content }
    return core.executePost(this.http, `/chat/room/${roomId}`, body)
  }

  // List chat rooms
  listRooms(filter) {
    const body = { filter }
    return core.executePost(this.http, '/chat/room/list', body)
  }

  // List chat rooms
  getRoomMessages(roomId, direction = 'b', limit = 10, start, end, messageFilter = {}) {
    const body = {
      room_id: roomId,
      direction,
      limit,
      start,
      end,
      message_filter: messageFilter,
    }
    return core.executePost(this.http, '/chat/room/messages', body)
  }

  // Get room events
  getRoomEvents(roomId, direction, limit, startToken, endToken) {
    const body = { room_id: roomId, direction, limit, start_token: startToken, end_token: endToken }
    return core.executePost(this.http, '/chat/room/events', body)
  }

  // Invite user to a room
  inviteToRoom(accountId, roomId) {
    const body = { account_id: accountId, room_id: roomId }
    return core.executePost(this.http, '/chat/room/invite', body)
  }

  // Get details of a room
  getRoomDetails(roomId) {
    const uri = `/chat/room/${roomId}`
    return core.executeGet(this.http, uri)
  }

  getRoomImage(roomId) {
    const uri = `/chat/room/${roomId}/image`
    return core.executeGet(this.http, uri)
  }

  // Get members of a room
  getRoomMembers(roomId) {
    const uri = `/chat/room/${roomId}/members`
    return core.executeGet(this.http, uri)
  }

  //Get org rooms
  getOrganizationRooms(orgId) {
    return core.executePost(this.http, `/chat/room/organization/${orgId}/rooms?organization_id=${orgId}`)
  }

  // Is room name unique
  isRoomNameUnique(name, orgId) {
    return core.executePost(this.http, `/chat/room/is_room_unique/organization/${orgId}/name/${name}`)
  }

  // Join a room
  // room must be an org room
  joinRoom(roomId) {
    return core.executePost(this.http, `/chat/room/${roomId}/join`, {})
  }

  // Get user IDs
  getUserIds() {
    return core.executePost(this.http, '/chat/user/get_user_ids', {})
  }

  // Ensure chat account
  ensureChatAccount() {
    return core.executePost(this.http, '/chat/user/ensure_chat_account', {})
  }

  updateRecieptMarker(roomId, eventId, receiptType = 'm.read', thread_id = 'main') {
    // console.log('UPDATE RECEIPT MARKER', roomId, eventId, receiptType, thread_id)
    return core.executePost(this.http, `/chat/room/${roomId}/receipt/${receiptType}/${eventId}`, { thread_id })
  }

  getRoomThreads(roomId, include = 'participated') {
    let uri = `/chat/room/${roomId}/threads?include=${include}`
    return core.executeGet(this.http, uri)
  }

  // Send a message to a room
  sendMessage(roomId, content, messageType = 'm.text', relatesToEventId = null, replyType = null) {
    const txId = `m-${Date.now()}`
    const body = { room_id: roomId, content, tx_id: txId, message_type: messageType }
    if (relatesToEventId) {
      body.relates_to_event_id = relatesToEventId
    }
    if (replyType) {
      body.reply_type = replyType
    }
    return core.executePost(this.http, `/chat/message/${roomId}`, body)
  }

  // Kick a user from a room
  kickUser(roomId, accountId, reason) {
    const body = { account_id: accountId, reason: reason }
    return core.executePost(this.http, `/chat/room/${roomId}/kick`, body)
  }

  // Leave the room
  leaveRoom(roomId) {
    return core.executePost(this.http, `/chat/room/${roomId}/leave`, {})
  }

  // Remove the room
  removeRoom(roomId) {
    return core.executeDelete(this.http, `/chat/room/${roomId}`, {})
  }

  //Suggestions
  getSuggestedMembers(roomId) {
    return core.executePost(this.http, `/chat/room/${roomId}/members/suggest`)
  }

  // Raw sync
  sync(timeout = 500, syncFilter = null, since = null, fullState = true, setPresence = null) {
    const body = {
      timeout,
      full_state: fullState,
    }
    if (syncFilter) {
      body.sync_filter = syncFilter
    } else {
      body.sync_filter = {
        room: {
          ephemeral: {
            limit: 0,
          },
          state: {
            unread_thread_notifications: true,
          },
          timeline: {
            unread_thread_notifications: true,
          },
        },
      }
    }
    if (since) {
      body.since = since
    }
    if (setPresence) {
      body.set_presence = setPresence
    }
    return core.executePost(this.http, '/chat/sync/', body)
  }

  ensurePusher() {
    return core.executePost(this.http, '/chat/notifications/ensure_chat_pusher')
  }

  resetPushers() {
    return core.executePost(this.http, '/chat/notifications/pushers/clear', {})
  }

  getPushers() {
    return core.executeGet(this.http, '/chat/notifications/pushers')
  }

  getMostFrequentReactions() {
    return core.executeGet(this.http, '/chat/reaction/most_frequent')
  }

  redactEvent(roomId, eventId, reason = 'deletion') {
    const body = { event_id: eventId, reason }
    return core.executePost(this.http, `/chat/event/redact/room/${roomId}`, body)
  }

  getEventRelations(roomId, eventId, from = null, limit = 20) {
    let uri = `/chat/event/room/${roomId}/relations/${eventId}?limit=${limit}`
    if (from) {
      uri = `/chat/event/room/${roomId}/relations/${eventId}?from=${from}&limit=${limit}`
    }
    return core.executeGet(this.http, uri)
  }

  getRoomEvent(roomId, eventId) {
    let uri = `/chat/room/${roomId}/event/${eventId}`
    return core.executeGet(this.http, uri)
  }

  replaceMessage(roomId, content, eventId) {
    const txId = `m-${Date.now()}`
    return core.executePost(this.http, `/chat/message/${roomId}/replace/${eventId}`, { content, tx_id: txId })
  }
}

export default ChatAPI
