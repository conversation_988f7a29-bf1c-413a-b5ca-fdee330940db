import core from './core'

class UsersAPI {
  constructor(http) {
    this.http = http
  }

  getMe() {
    const uri = '/users/profile_page'
    return core.executeGet(this.http, uri)
  }

  getCommunityUsers() {
    const uri = '/users/community_users'
    return core.executeGet(this.http, uri)
  }

  editMe(body) {
    const uri = '/users/me'
    return core.executePatch(this.http, uri, body)
  }

  editProfile(body) {
    const uri = '/users/profile_page'
    return core.executePatch(this.http, uri, body)
  }

  getProfile() {
    const uri = '/users/profile_page'
    return core.executeGet(this.http, uri)
  }

  getExternalProfile(uid) {
    const uri = `/users/user/${uid}`
    return core.executeGet(this.http, uri)
  }

  getImpact(aid) {
    const uri = `/users/impact_report/user/${aid}`
    return core.executeGet(this.http, uri)
  }
}

export default UsersAPI
