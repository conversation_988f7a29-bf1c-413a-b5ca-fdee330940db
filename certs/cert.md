# iOS

The password for the iOS key is `bluegato`


#Android

Original generator:
```bash
sudo keytool -genkey -v -keystore unified.keystore -alias unified-alias -keyalg RSA -keysize 2048 -validity 10000
```

```
keytool -list -v -keystore "unified.keystore" -alias androiddebugkey -storepass bluegato -keypass bluegato
```

The password for the Android key is `bluegato` (same as for keystore)

# Facebook for Android

`keytool -exportcert -alias <RELEASE_KEY_ALIAS> -keystore <RELEASE_KEY_PATH> | openssl sha1 -binary | openssl base64
`

`sudo keytool -exportcert -alias bluesquad-key-alias -keystore ./cert/bluesquad.keystore | openssl sha1 -binary | openssl base64
`

Result:
d1Dbh3z0dCeGSRrO+0kq7Yxi8F0=

qa app:
RE+EjW+w1ldDvc23kf1/DLCkVDk=


