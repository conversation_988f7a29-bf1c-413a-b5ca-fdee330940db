import React, { useState, useEffect } from 'react'
import { StyleSheet, View, Animated } from 'react-native'

const Confetti = () => {
  const [animation] = useState(new Animated.Value(0))

  useEffect(() => {
    Animated.timing(animation, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    }).start()
  }, [])

  const x = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [-150, 150],
  })
  const y = 100
  const translateX = Animated.multiply(Animated.multiply(x, x), 0.0025)
  const style = [styles.emoji, { transform: [{ translateX }, { translateY: y }] }]

  return (
    <View style={styles.container}>
      <Animated.Text style={style}>🎉</Animated.Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emoji: {
    fontSize: 50,
  },
})

export default Confetti
