/* eslint-disable react-native/no-inline-styles */
import React, { memo, useEffect, useState } from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, TouchableOpacity, Image, Pressable } from 'react-native'
import { medium } from '../../Utils/sizes'
import { unifiedDarkGray } from '../../Utils/colors'
import { useGlobalState } from '../../../contexts/store'
import { useCommunityState } from '../LocalStore'

const User = ({ item, index, handleNavigationProfile, dLength }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  //States
  const [globalState, globalActions] = useGlobalState()
  const [localState, localActions] = useCommunityState()

  const [following, setFollowing] = useState(item.is_following)

  useEffect(() => {
    setFollowing(item.is_following)
  }, [item.is_following])

  const unFollowUser = async aid => {
    try {
      const result = await globalState.api.followers.unfollowUser(aid)
      console.log('UNFOLLOW', result)
    } catch (e) {
      console.log('ERROR UNFOLLOW', e)
    }
  }

  const followUser = async aid => {
    try {
      const result = await globalState.api.followers.followUser(aid)
      console.log('FOLLOW', result)
    } catch (e) {
      console.log('ERROR FOLLOW', e)
    }
  }

  const hanldeOnPress = e => {
    setFollowing(!following)
    setTimeout(() => {
      const { id: aid } = e.account
      if (following) {
        unFollowUser(aid)
      } else {
        followUser(aid)
      }
      setTimeout(() => {
        localActions.setReloadKey()
      }, 150)
    }, 150)
  }

  return (
    <View style={{ backgroundColor: 'transparent', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 22, marginBottom: index + 1 === dLength ? 50 : 0 }}>
      <Pressable onPress={() => handleNavigationProfile(item)} style={{ flex: 1, backgroundColor: 'transparent', flexDirection: 'row', alignItems: 'center' }} key={`tt-${index}`}>
        <Image style={{ width: 42, height: 42, borderRadius: 60 }} source={item?.profile_pic ? { uri: item.profile_pic.uri } : require('../../../assets/images/noUser.png')} />
        <View style={{ marginLeft: 11 }}>
          <Text style={{ color: monochroma, fontWeight: medium, fontSize: 17 }}>{item.display_name}</Text>
          <Text numberOfLines={1} style={{ color: unifiedDarkGray, fontSize: 12 }}>
            {item.location ? `${item.location}` : ''}
            {item.profile.bio && item.location ? ' • ' : ''}
            {item.profile.bio ? item.profile.bio : ' '}
          </Text>
        </View>
      </Pressable>
      {!item.is_you && (
        <Pressable style={{ backgroundColor: 'transparent', height: '100%' }} onPress={() => hanldeOnPress(item)}>
          {following ? (
            <Text style={{ backgroundColor: 'transparent', color: '#CA09BC', fontSize: 14 }}>Following</Text>
          ) : (
            <Text style={{ backgroundColor: 'transparent', color: '#4778FB', fontSize: 14 }}>Follow</Text>
          )}
        </Pressable>
      )}
      {item.is_you && (
        <Pressable style={{ backgroundColor: 'transparent', height: '100%' }} onPress={() => ''}>
          <Text style={{ backgroundColor: 'transparent', color: '#CA09BC', fontSize: 14 }}>It's you!</Text>
        </Pressable>
      )}
    </View>
  )
}

export default User
