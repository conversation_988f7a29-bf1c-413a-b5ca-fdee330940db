/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, FlatList, Image, TouchableOpacity } from 'react-native'
import { medium } from '../../Utils/sizes'
import { unifiedDarkGray } from '../../Utils/colors'
import { executeNavigation } from './navigation'
import User from './User'

const UserList = ({ data = null, componentId }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  const [isNavigating, setNavigating] = useState(false)

  const handleNavigationProfile = async user => {
    if (isNavigating) {
      return
    }
    await setNavigating(true)
    await executeNavigation('Profile', componentId, { externalUser: { account_id: user.account.id } }, true)
    await setNavigating(false)
  }

  return (
    <>
      <FlatList
        data={data}
        style={{ flex: 1, backgroundColor: 'transparent', marginLeft: 22, marginRight: 22, maringBottom: 20 }}
        initialNumToRender={data.length}
        showsVerticalScrollIndicator={false}
        renderItem={({ item, index }) => <User dLength={data.length} index={index} handleNavigationProfile={handleNavigationProfile} item={item} />}
      />
    </>
  )
}

export default UserList
