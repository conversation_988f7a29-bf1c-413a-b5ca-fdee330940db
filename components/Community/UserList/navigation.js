import { Navigation } from 'react-native-navigation'
import { unifiedRoyalBlue } from '../../Utils/colors'

const generateOptions = (componentId, isProfile) => {
  const options = {
    bottomTabs: {
      visible: false,
      drawBehind: true,
    },
    topBar: {
      noBorder: true,
      scrollEdgeAppearance: {
        noBorder: true,
      },
      elevation: 0,
      visible: true,
      backButton: {
        color: { light: 'black', dark: 'white' },
      },
      background: {
        color: { light: 'white', dark: unifiedRoyalBlue },
      },
      title: {
        component: {
          name: 'ExpandedPostHeader',
        },
      },
    },
  }
  if (isProfile) {
    options.topBar = {
      visible: false,
      drawBehind: true,
      animate: false,
    }
  }
  return options
}

export const executeNavigation = async (name, componentId, passProps, isProfile = false) => {
  await Navigation.push(componentId, {
    component: {
      name,
      passProps: { ...passProps, popper: componentId },
      options: generateOptions(componentId, isProfile),
    },
  })
}
