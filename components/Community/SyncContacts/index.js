/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { memo, useContext, useEffect, useState } from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, Linking, ScrollView, FlatList, ActivityIndicator } from 'react-native'
import Pill from '../../Pill'
import { faAddressBook } from '@fortawesome/pro-solid-svg-icons'
import { Navigation } from 'react-native-navigation'
import { useGlobalState } from '../../../contexts/store'
import Contact from './Contact'
import { debounce } from 'lodash'
import { unifiedRoyalBlue } from '../../Utils/colors'
import { useMMKVBoolean } from 'react-native-mmkv'
import { ToastContext } from '../../Home/ToastStackWrapper'
const SyncContacts = ({ componentId }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  //Global State
  const [globalState] = useGlobalState()

  //States
  const [profile, setProfile] = useState(null)
  const [offSet, setOffSet] = useState(0)
  const [data, setData] = useState([])

  //MMKV
  const [confirmed = false] = useMMKVBoolean('IS_CONFIRMED', globalState.mmkv)

  //Context
  const { showConfirmActionSheet } = useContext(ToastContext)

  const getProfile = async () => {
    try {
      const response = await globalState.api.users.getProfile()
      await setProfile(response)
      await getContacts()
    } catch (e) {
      console.log('PROFILE ERROR', e)
      setTimeout(getProfile, 5000)
    }
  }

  const getContacts = debounce(async () => {
    try {
      console.log('SYNC', offSet)
      const result = await globalState.api.contacts.getContacts(offSet)
      if (result?.length > 0) {
        await setOffSet(offSet + 200)
        await setData([...data, ...result])
      }
    } catch (e) {
      console.log('CONTACT ERROR', JSON.stringify(e))
    }
  }, 1000)

  useEffect(() => {
    getProfile()
  }, [])

  const gotoSyncContacts = async () => {
    if (!confirmed) {
      showConfirmActionSheet('before syncing contacts.')
      return
    }
    await Navigation.push(componentId, {
      component: {
        name: 'SyncContacts',
        passProps: {
          updateTotal: () => {
            getProfile()
          },
        },
        options: {
          bottomTabs: {
            visible: false,
            drawBehind: true,
          },
          topBar: {
            noBorder: true,
            scrollEdgeAppearance: {
              noBorder: true,
            },
            elevation: 0,
            visible: true,
            background: {
              color: { light: 'white', dark: unifiedRoyalBlue },
            },
            backButton: {
              color: { light: 'black', dark: 'white' },
            },
            title: {
              component: {
                name: 'SyncContactsHeader',
              },
            },
          },
        },
      },
    })
  }

  const getText = () => {
    if (profile) {
      if (profile.synced_contacts > 0) {
        return `${profile.synced_contacts.toLocaleString()} synced contacts (contact profiles coming soon!)`
      } else {
        return 'You don\'t have any contacts.'
      }
    } else {
      return ''
    }
  }

  const getButtonText = () => {
    if (profile) {
      if (profile.synced_contacts > 0) {
        return 'Resync your phone contacts'
      } else {
        return 'Sync your phone contacts'
      }
    } else {
      return ''
    }
  }

  if (!profile) {
    return (
      <View style={{ flex: 1, backgroundColor: 'transparent', height: '100%', alignItems: 'center', justifyContent: 'center', paddingTop: 25 }}>
        <ActivityIndicator size="large" />
      </View>
    )
  }

  if (profile?.synced_contacts === 0) {
    return (
      <View style={styles.body}>
        <Text style={styles.text}>{getText()}</Text>
        <Pill icon={faAddressBook} text={getButtonText()} onPress={gotoSyncContacts} />
        <Text style={styles.text2}>Syncing your contacts makes it possible to take action on those contacts and build a community with those people, even if they're not on Unified.</Text>
        <Text style={styles.text2}>Unified never messages your contacts on its own.</Text>
        <Text onPress={() => Linking.openURL('https://joinunified.us/privacy-policy')} style={styles.link}>
          Learn more
        </Text>
      </View>
    )
  } else {
    return (
      <FlatList
        data={data}
        style={{ flex: 1, backgroundColor: 'transparent', marginLeft: 22, marginRight: 22, maringBottom: 20 }}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => `syn-${index}`}
        onEndReached={getContacts}
        ListHeaderComponent={() => {
          return (
            <View style={{ flex: 1, marginTop: 22 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
                <Text onPress={gotoSyncContacts} style={{ fontSize: 14, color: '#CA09BC', marginRight: 15 }}>
                  Re-sync contacts
                </Text>
                {/* <Text onPress={() => ''} style={{ fontSize: 14, color: '#CA09BC', marginLeft: 15 }}>
                  Add contact
                </Text> */}
              </View>
            </View>
          )
        }}
        renderItem={({ item, index }) => {
          return <Contact item={item} />
        }}
      />
    )
  }
}

export default memo(SyncContacts)
