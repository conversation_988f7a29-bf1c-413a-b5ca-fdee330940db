import { StyleSheet } from 'react-native'
import { unifiedRoyalBlue } from '../../Utils/colors'
export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

    return StyleSheet.create({
      body: {
        margin: 22,
      },
      text: {
        fontSize: 14,
        color: monochroma,
        marginBottom: 14,
      },
      text2: {
        fontSize: 14,
        color: monochroma,
        marginTop: 22,
      },
      link: {
        fontSize: 14,
        color: '#1DA1F2',
        marginTop: 22,
      },
    })
  }
}
