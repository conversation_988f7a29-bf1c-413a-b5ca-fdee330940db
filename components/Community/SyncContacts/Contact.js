/* eslint-disable react-native/no-inline-styles */
import React, { memo, useEffect, useState } from 'react'
import SendSMS from 'react-native-sms'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, TouchableOpacity, Image, Pressable, Linking, Platform } from 'react-native'
import { medium } from '../../Utils/sizes'
import { unifiedBlue, unifiedDarkGray } from '../../Utils/colors'
import { useGlobalState } from '../../../contexts/store'
import { useCommunityState } from '../LocalStore'
import { sendEmail } from '../../Utils/sendEmail'

const Contact = ({ item }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  //States
  const [globalState, globalActions] = useGlobalState()
  const [localState, localActions] = useCommunityState()

  const [following, setFollowing] = useState(item.is_following)

  useEffect(() => {
    setFollowing(item.is_following)
  }, [item.is_following])

  const unFollowUser = async aid => {
    try {
      const result = await globalState.api.followers.unfollowUser(aid)
      console.log('UNFOLLOW', result)
    } catch (e) {
      console.log('ERROR UNFOLLOW', e)
    }
  }

  const followUser = async aid => {
    try {
      const result = await globalState.api.followers.followUser(aid)
      console.log('FOLLOW', result)
    } catch (e) {
      console.log('ERROR FOLLOW', e)
    }
  }

  const hanldeOnPress = e => {
    setFollowing(!following)
    setTimeout(() => {
      const { id: aid } = e.account
      if (following) {
        unFollowUser(aid)
      } else {
        followUser(aid)
      }
      setTimeout(() => {
        localActions.setReloadKey()
      }, 150)
    }, 150)
  }

  const getLocation = () => {
    const { addresses } = item
    if (addresses) {
      let location
      addresses.forEach(e => {
        if (e.label === 'home') {
          if (e.city) {
            location = e.city
            if (e.state) {
              location += ', ' + e.state
            }
          } else if (e.state) {
            location = e.state
          }
        }
      })
      return location
    } else {
      return ''
    }
  }

  const startInvitationProcess = () => {
    const { phone_numbers, emails } = item

    const invite = `Hi ${item.name.display} - I recently joined Unified, a new social network for activism. If you’d like to join, you can sign up for their waitlist here on iOS: https://apps.apple.com/us/app/unified-activism-made-social/id1633477916 or here for Android: https://play.google.com/store/apps/details?id=com.unifiedforprogress.mobile`

    if (phone_numbers) {
      if (Platform.OS === 'android') {
        const url = `sms:${phone_numbers[0].number}?body=${invite}`
        Linking.openURL(url)
      } else {
        SendSMS.send(
          {
            body: invite,
            recipients: [phone_numbers[0].number],
            successTypes: ['sent', 'queued'],
            allowAndroidSendWithoutReadPermission: true,
          },
          async (completed, cancelled, error) => {
            console.log('SMS Callback: completed: ' + completed + ' cancelled: ' + cancelled + 'error: ' + error)
          },
        )
      }
    } else if (emails) {
      sendEmail(emails[0].email, 'Unified for Progress', invite)
    } else {
      console.log('NO CONTACT INFO')
      return
    }
  }

  return (
    <View style={{ backgroundColor: 'transparent', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 22 }}>
      <Pressable onPress={() => ''} style={{ flex: 1, backgroundColor: 'transparent', flexDirection: 'row', alignItems: 'center' }}>
        <Image style={{ width: 42, height: 42, borderRadius: 60 }} source={false ? { uri: item.profile_pic.uri } : require('../../../assets/images/noUser.png')} />
        <View style={{ marginLeft: 11, backgroundColor: 'transparent', flex: 1, marginRight: 20 }}>
          <Text numberOfLines={1} style={{ color: monochroma, backgroundColor: 'transparent', fontWeight: medium, fontSize: 17, maxWidth: '90%' }}>
            {item.name.display}
          </Text>
          <Text numberOfLines={1} style={{ color: unifiedDarkGray, fontSize: 12 }}>
            {getLocation() || 'Unknown'}
          </Text>
        </View>
      </Pressable>

      {/* <View style={{ backgroundColor: 'transparent', height: '100%', flexDirection: 'row' }}>
        <Pressable style={{ backgroundColor: 'transparent', paddingLeft: 10 }} onPress={startInvitationProcess}>
          <Text style={{ backgroundColor: 'transparent', color: '#F72585', fontSize: 14 }}>Remove</Text>
        </Pressable>
        <Pressable style={{ backgroundColor: 'transparent', paddingLeft: 16 }} onPress={startInvitationProcess}>
          <Text style={{ backgroundColor: 'transparent', color: unifiedBlue, fontSize: 14 }}>Invite</Text>
        </Pressable>
      </View> */}
    </View>
  )
}

export default Contact
