/* eslint-disable prettier/prettier */
/* eslint-disable react-native/no-inline-styles */
import { medium } from '../Utils/sizes'
import React, { useState } from 'react'
import StyleSheetFactory from './styles'
import SyncContacts from './SyncContacts'
import { TabView } from 'react-native-tab-view'
import { unifiedLightGray } from '../Utils/colors'
import Following from './IndependentLists/Following'
import Followers from './IndependentLists/Followers'
import { View, useColorScheme, useWindowDimensions, TouchableOpacity, Animated } from 'react-native'
import { unifiedRoyalBlue } from '../Utils/colors'

const Community = ({ componentId, isYou = false, aid = null, isOrg = false }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

  const layout = useWindowDimensions()


  const [index, setIndex] = React.useState(0)
  const [currentKey, setCurrentKey] = useState('')

  const [routes] = React.useState([
    { key: 'following', title: 'Following' },
    { key: 'followers', title: 'Followers' },
    { key: 'contacts', title: 'Contacts' },
    // { key: 'pending', title: 'Pending' },
  ])

  const [routesCompact] = React.useState([
    { key: 'following', title: 'Following' },
    { key: 'followers', title: 'Followers' },
  ])

  const [routesOrg] = React.useState([
    { key: 'followers', title: 'Followers' },
  ])


  const renderScene = ({ route }) => {
    switch (route.key) {
    case 'following':
      return <Following aid={aid} isYou={isYou} componentId={componentId} />
    case 'followers':
      return <Followers aid={aid} isYou={isYou} componentId={componentId} />
    case 'contacts':
      return <SyncContacts componentId={componentId} />
    default:
      return null
    }
  }

  const renderSceneCompact = ({ route }) => {
    switch (route.key) {
    case 'following':
      return <Following aid={aid} isYou={isYou} componentId={componentId} />
    case 'followers':
      return <Followers aid={aid} isYou={isYou} componentId={componentId} />
    default:
      return null
    }
  }

  const renderSceneOrg = ({ route }) => {
    switch (route.key) {
    case 'followers':
      return <Followers aid={aid} isYou={isYou} componentId={componentId} />
    default:
      return null
    }
  }

  const renderTabBar = props => {
    const inputRange = props.navigationState.routes.map((x, i) => i)

    return (
      <View style={styles.tabBar}>
        <View style={{ backgroundColor: unifiedLightGray, height: 2, width: '100%', position: 'absolute', bottom: 0, left:0 }} />
        {props.navigationState.routes.map((route, i) => {
          const opacity = props.position.interpolate({
            inputRange,
            outputRange: inputRange.map(inputIndex => (inputIndex === i ? 1 : 0.5)),
          })
          let styleBox
          if (index === i) {
            styleBox = { ...styles.tabItem, borderBottomColor: '#CA09BC', borderBottomWidth: 2 }
          } else {
            styleBox = { ...styles.tabItem }
          }
          if (i === 0) {
            styleBox.marginLeft = 10
          }

          const textStyle = index === i ? { ...styles.tabText, fontWeight: medium } : { ...styles.tabText}
          return (
            <TouchableOpacity key={`tab-${i}`} style={styleBox} onPress={() => {
              setIndex(i)
              setCurrentKey(route.key)
            }}>
              <Animated.Text style={textStyle} numberOfLines={1}>
                {route.title}
              </Animated.Text>
            </TouchableOpacity>
          )
        })}
      </View>
    )
  }

  if (isOrg) {
    return (
      <View style={{flex:1, backgroundColor:monochroma2}}>
        <Followers aid={aid} isYou={isYou} componentId={componentId} />
      </View>
    )
  }

  return (
    <View style={{ flex: 1, backgroundColor: monochroma2 }}>
      {isYou ? (
        <TabView renderTabBar={renderTabBar} navigationState={{ index, routes }} renderScene={renderScene} onIndexChange={setIndex} initialLayout={{ width: layout.width }} />
      ) : (
        <TabView renderTabBar={renderTabBar} navigationState={{ index, routes: isOrg ? routesOrg : routesCompact }} renderScene={isOrg ? renderSceneOrg : renderSceneCompact} onIndexChange={setIndex} initialLayout={{ width: layout.width }} />
      )}
    </View>
  )
}

export default Community
