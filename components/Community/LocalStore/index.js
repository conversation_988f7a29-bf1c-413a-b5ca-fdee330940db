/* eslint-disable prettier/prettier */
import { createStore, createHook } from 'react-sweet-state'

const Store = createStore({
  // value of the store on initialisation
  initialState: {
    reloadKey: '',
    activePost: null,
    allUsersList:null,
    followersList:null,
    followingList:null,
  },
  // actions that trigger store mutation
  actions: {
    // mutate state syncronously
    reset:
      () =>
        ({ setState, getState }) => {
          setState({
            reloadKey: '',
            activePost: null,
            allUsersList:null,
            followersList:null,
            followingList:null})
        },
    setReloadKey:
      () =>
        ({ setState, getState }) => {
          setState({ reloadKey:Math.random() })
        },
    setActivePost:
    (activePost = null) =>
      ({ setState, getState }) => {
        setState({ activePost })
      },
    setAllUsersList:
      (allUsersList = null) =>
        ({ setState, getState }) => {
          setState({ allUsersList })
        },
    setFollowersList:
      (followersList = null) =>
        ({ setState, getState }) => {
          setState({ followersList })
        },
    setFollowingList:
      (followingList = null) =>
        ({ setState, getState }) => {
          setState({ followingList })
        },
  },
  // optional, mostly used for easy debugging
  name: 'communityGlobal',
})

export const useCommunityState = createHook(Store)
