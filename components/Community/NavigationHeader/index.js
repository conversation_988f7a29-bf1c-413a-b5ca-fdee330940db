/* eslint-disable react-hooks/exhaustive-deps */
import React from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme } from 'react-native'

const NavigationHeader = ({ popTo, onTheFly = false }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  return (
    <View style={styles.main}>
      <Text style={styles.leftSideText}>Community</Text>
    </View>
  )
}

export default NavigationHeader
