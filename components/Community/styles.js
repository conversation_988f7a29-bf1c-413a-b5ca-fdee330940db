import { StatusBar, StyleSheet } from 'react-native'
import { unifiedRoyalBlue } from '../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

    return StyleSheet.create({
      body: {
        backgroundColor: monochroma2,
        flex: 1,
      },
      text: {
        color: monochroma2,
      },

      //TABS
      tabText: {
        color: monochroma,
      },
      tabBar: {
        flexDirection: 'row',
        backgroundColor: monochroma2,
        justifyContent: 'space-around',
        paddingTop: StatusBar.currentHeight,
      },
      tabItem: {
        // flex: 1,
        alignItems: 'center',
        padding: 10,
        fontSize: 14,
      },
    })
  }
}
