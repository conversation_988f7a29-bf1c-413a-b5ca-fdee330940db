import React from 'react'
import StyleSheetFactory from './styles'
import { Text, useColorScheme, TouchableOpacity } from 'react-native'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { unifiedBlue } from '../../../Utils/colors'
import { faMinusCircle, faPlusCircle, faTimes } from '@fortawesome/pro-solid-svg-icons'

const ListItem = ({ intial = false, obj, search = '', areaCode = '', text, chosen = false, removeChosenCode, addChosenCode }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  let tail = areaCode.slice(search.length)

  if (!areaCode.match(search)) {
    tail = areaCode
    search = ''
  }

  if (intial && !chosen) {
    return null
  }

  if (chosen) {
    return null
  }

  return (
    <TouchableOpacity
      onPress={() => {
        if (chosen) {
          removeChosenCode(obj)
        } else {
          addChosenCode(obj)
        }
      }}
      style={styles.listItem}>
      {chosen ? (
        <Text numberOfLines={1} style={styles.listItemText}>
          {areaCode}, {text}
        </Text>
      ) : (
        <Text numberOfLines={1} style={styles.listItemText}>
          <Text style={styles.bold}>{search}</Text>
          {tail}, {text}
        </Text>
      )}
      {chosen ? <FontAwesomeIcon color="#F72585" size={20} icon={faMinusCircle} /> : <FontAwesomeIcon color={unifiedBlue} size={20} icon={faPlusCircle} />}
    </TouchableOpacity>
  )
}

export default ListItem
