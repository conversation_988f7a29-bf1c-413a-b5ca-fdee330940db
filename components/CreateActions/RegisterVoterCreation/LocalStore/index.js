/* eslint-disable prettier/prettier */
import { createStore, createHook } from 'react-sweet-state'

const Store = createStore({
  // value of the store on initialisation
  initialState: {
    title: '',
    say: '',
    filter: [],
    tab: 'area',
    currentPoster: null,
    selectedAreaCodes: [],
    statuses: [],
  },
  // actions that trigger store mutation
  actions: {
    // mutate state syncronously
    reset:
      () =>
        ({ setState, getState }) => {
          setState({ title: '', say: '', filter: [], tab: 'area', selectedAreaCodes: [], statuses: [], currentPoster: null})
        },
    setCurrentPoster:
      (currentPoster = null) =>
        ({ setState, getState }) => {
          setState({ currentPoster })
        },
    setStatuses:
      (statuses = []) =>
        ({ setState, getState }) => {
          setState({ statuses })
        },
    setTab:
      (tab = 'area') =>
        ({ setState, getState }) => {
          setState({ tab })
        },
    setSelectedAreaCodes:
      (selectedAreaCodes = []) =>
        ({ setState, getState }) => {
          setState({ selectedAreaCodes })
        },
    setTitle:
      (title = '') =>
        ({ setState, getState }) => {
          setState({ title })
        },
    setSay:
      (say = '') =>
        ({ setState, getState }) => {
          setState({ say })
        },
    setFilter:
      (filter = []) =>
        ({ setState, getState }) => {
          setState({ filter })
        },
  },
  // optional, mostly used for easy debugging
  name: 'voterRegistrationGlobal',
})

export const useVoterRegState = createHook(Store)
