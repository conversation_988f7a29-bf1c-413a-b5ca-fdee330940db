/* eslint-disable react-native/no-inline-styles */
import StyleSheetFactory from './styles'
import React, { useEffect, useRef, useState } from 'react'
import { faAngleDown } from '@fortawesome/pro-regular-svg-icons'
import ActionCreationWrapper from '../../../Wrappers/ActionCreationWrapper'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { closeMenu, createResponder, init } from './PanResponderInstance'
import { Text, useColorScheme, View, Modal, TouchableOpacity, Animated } from 'react-native'
import { useVoterRegState } from '../LocalStore'

const FullScreenSheet = ({ visible, setVisible, HeaderComponent, children }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  const [pan] = useState(new Animated.ValueXY())
  const opacity = useRef(new Animated.Value(0)).current

  const panResponder = createResponder(pan, setVisible, opacity)
  const panStyle = { transform: pan.getTranslateTransform() }

  const [display, setDisplay] = useState('none')
  const [vrState, vrActions] = useVoterRegState()

  useEffect(() => {
    const initAnimation = () => init(pan, opacity, setDisplay)
    if (visible) {
      initAnimation()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <Modal onRequestClose={() => closeMenu(pan, setVisible, opacity)} visible={visible} transparent animationType="none">
      <>
        <Animated.View style={{ ...styles.closer, opacity }}>
          <TouchableOpacity onPress={() => closeMenu(pan, setVisible, opacity)} style={styles.closerTouchable} />
        </Animated.View>
        <Animated.View style={[styles.background, panStyle, { display }]}>
          <ActionCreationWrapper>
            <View {...panResponder.panHandlers} style={{ backgroundColor: 'transparent' }}>
              <View style={styles.header}>
                <View style={styles.compoundTitle}>
                  <TouchableOpacity style={styles.closingMechanism} onPress={() => closeMenu(pan, setVisible, opacity)}>
                    <FontAwesomeIcon color={monochroma} size={20} icon={faAngleDown} />
                    <Text style={styles.closingMechanismText}>Contact filter</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.closingMechanism} onPress={() => closeMenu(pan, setVisible, opacity)}>
                    <Text style={[styles.title, { color: vrState.selectedAreaCodes.length > 0 ? '#4778FB' : '#BABABA' }]}>Done</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            {children}
          </ActionCreationWrapper>
        </Animated.View>
      </>
    </Modal>
  )
}

export default FullScreenSheet
