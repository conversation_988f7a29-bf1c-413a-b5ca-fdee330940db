import { StyleSheet } from 'react-native'
import { unifiedBlue, unifiedDarkGray } from '../../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      background: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        backgroundColor: unifiedDarkGray,
        opacity: 0.3,
      },
      container: {
        flex: 1,
        width: '100%',
        height: '100%',
        paddingLeft: 16,
        paddingRight: 16,
        justifyContent: 'center',
        alignItems: 'center',
      },
      modal: {
        backgroundColor: unifiedBlue,
        height: 97,
        padding: 20,
        width: '100%',
        borderRadius: 16,
      },
      text: {
        fontSize: 14,
        color: 'white',
      },
    })
  }
}
