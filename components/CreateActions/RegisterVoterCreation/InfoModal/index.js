import React from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, Modal, TouchableWithoutFeedback } from 'react-native'

const InfoModal = ({ info, setInfo }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  return (
    <Modal visible={info} transparent animationType="fade">
      <>
        <View style={styles.background} />
        <TouchableWithoutFeedback onPress={() => setInfo(false)}>
          <View style={styles.container}>
            <View style={styles.modal}>
              <Text style={styles.text}>Use the contact filter to ask your followers to target specific contacts in their respective communities.</Text>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </>
    </Modal>
  )
}

export default InfoModal
