import React from 'react'
import StyleSheetFactory from './styles'
import { Text, useColorScheme, TouchableOpacity } from 'react-native'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { unifiedBlue } from '../../../Utils/colors'
import { faMinusCircle, faPlusCircle, faTimes } from '@fortawesome/pro-solid-svg-icons'

const ListItem = ({ text, chosen = false, toggleChosen }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  return (
    <TouchableOpacity onPress={() => toggleChosen(text)} style={styles.listItem}>
      <Text numberOfLines={1} style={styles.listItemText}>
        {text}
      </Text>
      {chosen ? <FontAwesomeIcon color="#F72585" size={20} icon={faMinusCircle} /> : <FontAwesomeIcon color={unifiedBlue} size={20} icon={faPlusCircle} />}
    </TouchableOpacity>
  )
}

export default ListItem
