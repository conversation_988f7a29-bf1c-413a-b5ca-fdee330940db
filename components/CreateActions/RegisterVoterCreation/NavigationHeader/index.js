/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useState } from 'react'
import debounce from 'lodash.debounce'
import StyleSheetFactory from './styles'
import { useVoterRegState } from '../LocalStore'
import { Navigation } from 'react-native-navigation'
import { useGlobalState } from '../../../../contexts/store'
import { View, Text, useColorScheme, Alert, TouchableOpacity, ActivityIndicator, PixelRatio } from 'react-native'
import { useCreatePostState } from '../../../CreatePost/LocalStore'
import { useProfileState } from '../../../Profile/LocalStore'
import { useHomeFeedState } from '../../../Home/LocalStore'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const NavigationHeader = ({ popTo, onTheFly = false }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const fontScale = PixelRatio.getFontScale()
  const isFontScaled = fontScale === 1.353 // 1.353 is the scale to change design

  // States
  const [state, actions] = useVoterRegState()
  const [postState, postActions] = useCreatePostState()
  const [globalState, globalActions] = useGlobalState()
  const [isPublishing, setIsPublishing] = useState(false)
  const [profileState, profileActions] = useProfileState()
  const [homeFeedState, homeFeedActions] = useHomeFeedState()

  useEffect(() => {
    console.log('CI', popTo)
  }, [])

  const assembleFilter = () => {
    const { tab, selectedAreaCodes, statuses } = state
    let filter = {}
    if (tab === 'area') {
      filter.area_code = selectedAreaCodes.map(e => e.area_code)
    } else {
      console.log('STATUS', statuses)
      filter.voter_reg_status = statuses.filter(e => e.chosen).map(e => e.slug)
    }
    return filter
  }

  const publish = async () => {
    if (isPublishing) {
      return
    }
    await setIsPublishing(true)
    try {
      const { say } = state
      const title = say.substring(0, 50)
      const filter = assembleFilter()
      console.log('PUBLISHING', state.currentPoster)
      const orgId = state.currentPoster ? state.currentPoster.account.id : null
      const result = await globalState.api.actions.createAction(title, say, new Date(), 1, filter, null, null, null, orgId)
      if (onTheFly) {
        console.log('OTF', result)
        postActions.addAction(result)
        Navigation.popTo(popTo)
        actions.reset()
        return
      }
      await globalState.api.posts.createPost(' ', ' ', [{ content_type: 'action', object_id: result.id, order: 0 }], null, orgId)
      // globalActions.setLastAction(result)
      await setIsPublishing(false)
      profileActions.setReloadKey()
      homeFeedActions.setReloadKey()
      Navigation.popTo(popTo)
      actions.reset()
    } catch (e) {
      await setIsPublishing(false)
      let error = 'Please try again.'
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e?.info?.responseBody?.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else {
          error = e?.info?.responseBody?.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
    }
  }
  const { setToasts, toastRef } = useContext(ToastContext)

  const isReady = () => {
    const { say } = state
    return say.trim() !== ''
  }

  return (
    <View style={styles.main}>
      <Text style={styles.leftSideText}>{isFontScaled ? 'New action' : 'New voter registration action'}</Text>
      {!isPublishing && (
        <TouchableOpacity
          disabled={!isReady()}
          onPress={() => {
            if (isReady()) {
              publish()
            }
          }}>
          <Text style={isReady() ? styles.publish : styles.publishD}>Publish</Text>
        </TouchableOpacity>
      )}
      {isPublishing && <ActivityIndicator size={'small'} color={monochroma} />}
    </View>
  )
}

export default NavigationHeader
