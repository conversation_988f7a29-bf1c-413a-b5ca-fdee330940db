/* eslint-disable react-native/no-inline-styles */
/* eslint-disable operator-linebreak */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useState } from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, TouchableOpacity, TextInput, Alert, useWindowDimensions, Animated, Pressable } from 'react-native'
import FullScreenSheet from '../FullScreenSheet'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { faMapMarkerAlt, faPhone, faRegistered, faTimes } from '@fortawesome/pro-solid-svg-icons'
import { faLobster, faSearch } from '@fortawesome/pro-regular-svg-icons'
import { useGlobalState } from '../../../../contexts/store'
import ListItemStatus from '../ListItemStatus'
import ListItemArea from '../ListItemArea'
import { useVoterRegState } from '../LocalStore'
import { unifiedLightGray, unifiedPinkRed, unifiedPurple, unifiedRoyalBlue } from '../../../Utils/colors'
import { SceneMap, TabView } from 'react-native-tab-view'
import { medium } from '../../../Utils/sizes'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const ContactFilter = ({ visible, setVisible }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const monochromaD = unifiedLightGray

  //Global State
  const [state, actions] = useGlobalState()
  const [vrState, vrActions] = useVoterRegState()
  const { tab, selectedAreaCodes, statuses } = vrState
  const { setSelectedAreaCodes, setTab, setStatuses } = vrActions

  const [areas, setAreas] = useState([])
  const [search, setSearch] = useState('')

  useEffect(() => {}, [tab, selectedAreaCodes, statuses])

  const handleActionPress = () => {
    setVisible(true)
  }

  const handleTabSelection = rawTab => {
    setTab(rawTab)
    setSearch('')
  }

  const { setToasts, toastRef } = useContext(ToastContext)

  useEffect(() => {
    const loadAreas = async () => {
      try {
        const result = await state.api.lookups.listAreaCodes(search)
        setAreas(result)
      } catch (e) {
        let error = 'Please try again.'
        if (e?.info?.responseBody?.detail) {
          if (Array.isArray(e?.info?.responseBody?.detail)) {
            error = e.info.responseBody.detail.join('/n')
          } else {
            error = e?.info?.responseBody?.detail
          }
        }
        setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
        toastRef.current.show()
      }
    }
    loadAreas()
  }, [search])

  useEffect(() => {
    const loadStatus = async () => {
      if (statuses.length === 0) {
        try {
          const result = await state.api.lookups.listVoterRegStates()
          setStatuses(result)
        } catch (e) {
          let error = 'Please try again.'
          if (e?.info?.responseBody?.detail) {
            if (Array.isArray(e?.info?.responseBody?.detail)) {
              error = e.info.responseBody.detail.join('/n')
            } else {
              error = e?.info?.responseBody?.detail
            }
          }
          setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
          toastRef.current.show()
        }
      }
    }
    loadStatus()
  }, [])

  const toggleChosen = description => {
    const newStatuses = statuses.map((e, i) => {
      if (e.description === description) {
        if (e.chosen) {
          e.chosen = false
        } else {
          e.chosen = true
        }
      }
      return e
    })
    setStatuses(newStatuses)
  }

  const addChosenCode = areaCode => {
    areaCode.chosen = true
    const newCodes = [...selectedAreaCodes, areaCode]
    setSelectedAreaCodes(newCodes)
  }

  const removeChosenCode = obj => {
    const newCodes = selectedAreaCodes.filter(e => e.area_code !== obj.area_code)
    setSelectedAreaCodes(newCodes)
  }

  const generateRenderAreas = () => {
    const codes = selectedAreaCodes.map(e => e.area_code)
    console.log('SELECTED AREAS', codes)
    if (codes.length > 0) {
      const newAreas = areas.filter(a => !codes.includes(a.area_code))
      const r = [...newAreas].map(e => {
        delete e.chosen
        return e
      })
      console.log('GENERATING AREAS', r)
      return r
    } else {
      const newAreas = [...areas].map(e => {
        delete e.chosen
        return e
      })
      console.log('GENERATING AREAS DEFAULT', newAreas)
      return newAreas
    }
  }

  const handleSearch = text => {
    setSearch(text)
  }

  const [index, setIndex] = React.useState(0)

  const InnerAreaCode = () => {
    return (
      <View style={[styles.container, { backgroundColor: 'red' }]}>
        <View style={styles.searchContainer}>
          <TextInput value={search} onChangeText={text => handleSearch(text)} placeholderTextColor={unifiedLightGray} placeholder="Search" style={styles.search} />
          <FontAwesomeIcon color="black" size={22} icon={faSearch} />
        </View>

        {generateRenderAreas().length > 0 &&
          generateRenderAreas().map((e, i) => {
            return (
              <ListItemArea
                removeChosenCode={removeChosenCode}
                addChosenCode={addChosenCode}
                intial={search.trim() === ''}
                areaCode={e.area_code}
                text={e.description}
                chosen={e?.chosen}
                search={search}
                key={`li-${i}`}
                obj={e}
              />
            )
          })}
      </View>
    )
  }

  const layout = useWindowDimensions()

  const [routes] = React.useState([
    { key: 'areCode', title: 'Area code' },
    { key: 'areCode', title: 'Area code' },
  ])

  const renderScene = SceneMap({
    areCode: InnerAreaCode,
  })

  const renderTabBar = props => {
    const inputRange = props.navigationState.routes.map((x, i) => i)

    return (
      <View style={styles.tabBar}>
        <View style={{ backgroundColor: unifiedLightGray, height: 2, width: '100%', position: 'absolute', bottom: 0 }} />
        {props.navigationState.routes.map((route, i) => {
          const opacity = props.position.interpolate({
            inputRange,
            outputRange: inputRange.map(inputIndex => (inputIndex === i ? 1 : 0.5)),
          })
          let styleBox
          if (index === i) {
            styleBox = { ...styles.tabItem, borderBottomColor: 'black', borderBottomWidth: 2 }
          } else {
            styleBox = { ...styles.tabItem }
          }
          if (i === 0) {
            styleBox.marginLeft = 10
          }
          if (i === 1) {
            styleBox.marginLeft = 30
          }
          const getIcon = () => {
            return faPhone
          }
          return (
            <TouchableOpacity key={`tab-${i}`} style={styleBox} onPress={() => setIndex(i)}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <FontAwesomeIcon icon={getIcon()} color={monochroma} size={25} />
                <Animated.Text style={[styles.tabText, { opacity }, { marginLeft: 8 }]} numberOfLines={1}>
                  {route.title}
                </Animated.Text>
              </View>
            </TouchableOpacity>
          )
        })}
      </View>
    )
  }
  // <View>
  //   <TabView renderTabBar={renderTabBar} navigationState={{ index, routes }} renderScene={renderScene} onIndexChange={setIndex} initialLayout={{ width: layout.width }} />
  // </View>

  return (
    <FullScreenSheet visible={visible} setVisible={setVisible}>
      <View style={{ paddingLeft: 22, paddingRight: 22, paddingBottom: 28, paddingTop: 26 }}>
        <Text style={{ color: monochroma, fontSize: 14, marginBottom: 6 }}>Target your action to contacts that match these criteria:</Text>
        <View style={{ flexDirection: 'row', flex: 1, backgroundColor: 'transparent', flexWrap: 'wrap' }}>
          {vrState.selectedAreaCodes.map((e, i) => {
            return (
              <Pressable key={`kxc-${i}`} onPress={() => removeChosenCode(e)}>
                <View
                  style={{
                    marginRight: 8,
                    width: 140,
                    justifyContent: 'space-between',
                    borderRadius: 8,
                    flexDirection: 'row',
                    padding: 11,
                    alignItems: 'center',
                    backgroundColor: '#E8FBF2',
                    marginTop: 8,
                  }}>
                  <Text>{e.area_code} area code</Text>
                  <FontAwesomeIcon icon={faTimes} color="black" size={14} />
                </View>
              </Pressable>
            )
          })}
        </View>
      </View>
      <View style={{ backgroundColor: 'transparent', paddingLeft: 22, paddingBottom: 11 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <FontAwesomeIcon icon={faPhone} color={monochroma} size={25} />
          <Text style={{ color: monochroma, fontSize: 14, fontWeight: medium, marginLeft: 11 }} numberOfLines={1}>
            Area code
          </Text>
        </View>
      </View>
      <View style={{ width: '100%', backgroundColor: '#D9D9D9', height: 1, paddingLeft: 9, marginBottom: 28 }}>
        <View style={{ width: 126, backgroundColor: unifiedPurple, height: 1 }} />
      </View>
      <View style={[styles.container, { backgroundColor: 'transparent' }]}>
        <View style={styles.searchContainer}>
          <TextInput value={search} onChangeText={text => handleSearch(text)} placeholderTextColor={unifiedLightGray} placeholder="Search" style={styles.search} />
          <FontAwesomeIcon color="black" size={22} icon={faSearch} />
        </View>

        {generateRenderAreas().length > 0 &&
          generateRenderAreas().map((e, i) => {
            return (
              <ListItemArea
                removeChosenCode={removeChosenCode}
                addChosenCode={addChosenCode}
                intial={search.trim() === ''}
                areaCode={e.area_code}
                text={e.description}
                chosen={e?.chosen}
                search={search}
                key={`li-${i}`}
                obj={e}
              />
            )
          })}
      </View>
    </FullScreenSheet>
  )
}

export default ContactFilter
