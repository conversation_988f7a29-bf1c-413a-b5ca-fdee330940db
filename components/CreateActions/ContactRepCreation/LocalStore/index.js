/* eslint-disable prettier/prettier */
import { createStore, createHook } from 'react-sweet-state'

const Store = createStore({
  // value of the store on initialisation
  initialState: {
    say: '',
    title: '',
    subject: '',
    body: '',
    filter: [],
    tab: 'rep',
    currentPoster: null,
    selectedOffice: null,
    selectedOfficial: null,
  },
  // actions that trigger store mutation
  actions: {
    // mutate state syncronously
    reset:
      () =>
        ({ setState, getState }) => {
          setState({ title: '', say: '', filter: [], tab: 'rep', selectedOfficial: null, selectedOffice: null, subject:'', body:'', setCurrentPoster:null })
        },
    setCurrentPoster:
      (currentPoster = null) =>
        ({ setState, getState }) => {
          setState({ currentPoster })
        },
    setBody:
      (body = '') =>
        ({ setState, getState }) => {
          setState({ body })
        },
    setSubject:
      (subject = '') =>
        ({ setState, getState }) => {
          setState({ subject })
        },
    setSelectedOffice:
      (selectedOffice = null) =>
        ({ setState, getState }) => {
          setState({ selectedOffice })
        },
    setTab:
      (tab = 'rep') =>
        ({ setState, getState }) => {
          setState({ tab })
        },
    setSelectedOfficial:
      (selectedOfficial = null) =>
        ({ setState, getState }) => {
          setState({ selectedOfficial })
        },
    setTitle:
      (title = '') =>
        ({ setState, getState }) => {
          setState({ title })
        },
    setSay:
      (say = '') =>
        ({ setState, getState }) => {
          setState({ say })
        },
    setFilter:
      (filter = []) =>
        ({ setState, getState }) => {
          setState({ filter })
        },
  },
  // optional, mostly used for easy debugging
  name: 'contactRepGlobal',
})

export const useContactRep = createHook(Store)
