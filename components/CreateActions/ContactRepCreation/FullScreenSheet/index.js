/* eslint-disable react-native/no-inline-styles */
import StyleSheetFactory from './styles'
import React, { useEffect, useRef, useState } from 'react'
import { faAngleDown } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { closeMenu, createResponder, init } from './PanResponderInstance'
import { Text, useColorScheme, View, Modal, TouchableOpacity, Animated } from 'react-native'
import ActionCreationWrapper from '../../../Wrappers/ActionCreationWrapper'
import { useContactRep } from '../LocalStore'
import { unifiedBlue } from '../../../Utils/colors'

const FullScreenSheet = ({ visible, setVisible, HeaderComponent, children }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  const [pan] = useState(new Animated.ValueXY())
  const opacity = useRef(new Animated.Value(0)).current

  const panResponder = createResponder(pan, setVisible, opacity)
  const panStyle = { transform: pan.getTranslateTransform() }

  const [display, setDisplay] = useState('none')
  const [state, actions] = useContactRep()
  const { selectedOffice, selectedOfficial } = state

  useEffect(() => {
    const initAnimation = () => init(pan, opacity, setDisplay)
    if (visible) {
      initAnimation()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const childrenWithProps = React.Children.map(children, child => {
    // Checking isValidElement is the safe way and avoids a typescript
    // error too.
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { closeMenu: () => closeMenu(pan, setVisible, opacity) })
    }
    return child
  })

  const isAnythingSelected = () => {
    return selectedOffice || selectedOfficial
  }

  return (
    <Modal onRequestClose={() => closeMenu(pan, setVisible, opacity)} visible={visible} transparent animationType="none">
      <>
        <Animated.View style={{ ...styles.closer, opacity }}>
          <TouchableOpacity onPress={() => closeMenu(pan, setVisible, opacity)} style={styles.closerTouchable} />
        </Animated.View>
        <Animated.View style={[styles.background, panStyle, { display }]}>
          <ActionCreationWrapper>
            <View {...panResponder.panHandlers} style={{ backgroundColor: 'transparent' }}>
              <View style={styles.header}>
                <View style={styles.compoundTitle}>
                  <TouchableOpacity style={styles.closingMechanism} onPress={() => closeMenu(pan, setVisible, opacity)}>
                    <FontAwesomeIcon color={monochroma} size={20} icon={faAngleDown} />
                    <Text style={styles.closingMechanismText}>Select representative</Text>
                  </TouchableOpacity>
                  {/* <TouchableOpacity
                    style={{ backgroundColor: isAnythingSelected() ? unifiedBlue : '#D9D9D9', paddingLeft: 16, paddingRight: 16, paddingTop: 5, paddingBottom: 5 }}
                    onPress={() => closeMenu(pan, setVisible, opacity)}>
                    <Text style={styles.title}>Select</Text>
                  </TouchableOpacity> */}
                </View>
              </View>
              {/* <HeaderComponent /> */}
            </View>
            {childrenWithProps}
          </ActionCreationWrapper>
        </Animated.View>
      </>
    </Modal>
  )
}

export default FullScreenSheet
