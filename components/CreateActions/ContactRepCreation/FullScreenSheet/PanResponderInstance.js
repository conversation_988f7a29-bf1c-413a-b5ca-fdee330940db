import { Animated, PanResponder } from 'react-native'

const isDropArea = gesture => gesture.dy > 200
const opacityFloor = 0.3

export const init = (pan, opacity, setDisplay) => {
  pan.setValue({ x: 0, y: 1000 })
  setDisplay('flex')
  Animated.spring(pan, {
    toValue: { x: 0, y: 0 },
    stiffness: 40,
    damping: 10,
    overshootClamping: true,
    mass: 0.3,
    useNativeDriver: false,
  }).start()
  setTimeout(() => {
    Animated.timing(opacity, {
      toValue: 0.3,
      duration: 200,
      useNativeDriver: false,
    }).start()
  }, 350)
}

export const closeMenu = (pan, setVisible, opacity) => {
  Animated.spring(pan, {
    toValue: { x: 0, y: 1000 },
    stiffness: 40,
    damping: 10,
    mass: 1,
    useNativeDriver: false,
  }).start(() => {})
  Animated.timing(opacity, {
    toValue: 0,
    duration: 100,
    useNativeDriver: false,
  }).start()
  setTimeout(() => {
    setVisible(false)
  }, 400)
}

export const createResponder = (pan, setVisible, opacity) => {
  return PanResponder.create({
    // true so that panResponder starts responding to touch events
    onStartShouldSetPanResponder: () => true,
    onPanResponderGrant: (e, gesture) => {
      pan.setValue({ x: 0, y: 0 })
    },
    onPanResponderMove: (e, gestureState) => {
      // Configure Min and Max Values
      const MaxDistance = 1000
      const MinDistance = 0
      // eslint-disable-next-line radix
      const dyCapped = Math.min(Math.max(parseInt(gestureState.dy), MinDistance), MaxDistance)
      // If within our bounds, use our gesture.dy....else use dyCapped
      const values = {}
      if (gestureState.dy < MaxDistance && gestureState.dy > MinDistance) {
        values.dx = gestureState.dx
        values.dy = gestureState.dy
      } else {
        values.dy = dyCapped
        values.dx = gestureState.dx
      }

      const degrading = (opacityFloor * (100 - values.dy)) / 100

      Animated.timing(opacity, {
        toValue: degrading < 0 ? 0 : degrading,
        duration: 0,
        useNativeDriver: false,
      }).start()

      //Animate Event
      Animated.event(
        [
          null,
          {
            dy: pan.y,
          },
        ],
        { useNativeDriver: false },
      )(e, values)
    },
    // when touch is released, go back to original in a spring motion
    onPanResponderRelease: (e, gesture) => {
      if (isDropArea(gesture)) {
        closeMenu(pan, setVisible, opacity)
      } else {
        Animated.spring(pan, {
          toValue: { x: 0, y: 0 },
          bounciness: 0,
          useNativeDriver: false,
        }).start()
        Animated.timing(opacity, {
          toValue: opacityFloor,
          duration: 220,
          useNativeDriver: false,
        }).start()
      }
    },
  })
}
