import { Platform, StyleSheet } from 'react-native'
import DeviceInfo from 'react-native-device-info'
import { darkModeGray, unifiedDarkGray, unifiedRoyalBlue } from '../../../Utils/colors'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

    return StyleSheet.create({
      closerTouchable: {
        flex: 1,
        width: '100%',
        height: '100%',
      },
      closer: {
        top: 0,
        flex: 1,
        width: '100%',
        height: '100%',
        position: 'absolute',
        backgroundColor: unifiedDarkGray,
      },
      mainPost: {
        // flexShrink: 1,
        paddingLeft: 20,
        paddingRight: 20,
        paddingBottom: 20,
        backgroundColor: 'transparent',
      },
      options: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        paddingTop: 12,
        paddingLeft: 20,
        paddingRight: 20,
        backgroundColor: monochroma2, //mono2
        // borderBottomColor: '#CECECE',
        // borderBottomWidth: 1,
      },
      option: {
        width: 50,
        alignItems: 'center',
        // backgroundColor: 'red',
      },
      optionAction: {
        fontSize: 12,
        textAlign: 'center',
        // backgroundColor: 'orange',
        color: monochroma,
        fontWeight: medium,
        width: 50,
        marginLeft: 20,
        marginBottom: 16,
        // paddingRight: 20,
      },
      text: {
        fontSize: 14,
        color: monochroma,
        lineHeight: 20,
      },
      name: {
        fontSize: 14,
        marginLeft: 8,
        color: monochroma,
      },
      person: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingBottom: 11,
        paddingLeft: 20,
        paddingRight: 20,
        backgroundColor: monochroma2,
      },
      compoundTitle: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      },
      closingMechanism: {
        flexDirection: 'row',
      },
      closingMechanismText: {
        fontSize: 16,
        fontWeight: medium,
        color: monochroma,
        marginLeft: 11,
      },
      title: {
        fontSize: 16,
        fontWeight: medium,
        color: 'white',
      },
      date: {
        fontSize: 12,
        color: isDarkMode ? 'white' : unifiedDarkGray,
        marginBottom: 12,
        paddingLeft: 20,
        paddingRight: 20,
        backgroundColor: monochroma2,
      },
      header: {
        paddingTop: 22,
        paddingLeft: 20,
        paddingRight: 20,
        marginBottom: 0,
        backgroundColor: 'transparent',
      },
      picture: {
        width: 32,
        height: 32,
        borderRadius: 999,
        backgroundColor: 'gray',
      },
      list: {
        zIndex: 9999,
        overflow: 'hidden',
        backgroundColor: 'transparent',
        // height: 300,
        flex: 1,
      },
      background: {
        flex: 1,
        shadowColor: isDarkMode ? 'black' : '#999999',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 1,
        shadowRadius: 4,
        elevation: 8,
        backgroundColor: monochroma2,
        height: '100%',
        // marginTop: Platform.select({ ios: DeviceInfo.hasNotch() ? 50 : 40, android: 10 }),
      },
    })
  }
}
