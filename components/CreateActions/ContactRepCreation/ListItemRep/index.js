import React from 'react'
import StyleSheetFactory from './styles'
import { Text, useColorScheme, TouchableOpacity } from 'react-native'
import { useContactRep } from '../LocalStore'

const ListItemRep = ({ obj = {}, search = '', repText = '', text, closeMenu }) => {
  console.log('OFFICIAL', obj)
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  let tail = repText.slice(search.length)

  //State
  const [state, actions] = useContactRep()

  if (!repText.match(search)) {
    tail = repText
    search = ''
  }

  const handleOnPress = () => {
    actions.setTab('rep')
    actions.setSelectedOfficial(obj)
    closeMenu()
  }

  return (
    <TouchableOpacity onPress={handleOnPress} style={styles.listItem}>
      <Text numberOfLines={1} style={styles.listItemText}>
        <Text style={styles.bold}>{search}</Text>
        {tail}
        {text}
      </Text>
      <Text style={styles.selectButton}>Select</Text>
    </TouchableOpacity>
  )
}

export default ListItemRep
