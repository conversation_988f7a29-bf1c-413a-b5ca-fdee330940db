import React from 'react'
import StyleSheetFactory from './styles'
import { Platform, Text, TextInput, useColorScheme, View } from 'react-native'
import { useContactRep } from '../LocalStore'

const Email = () => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  const [state, actions] = useContactRep()

  return (
    <View style={styles.emailContainer}>
      <Text style={styles.subject}>Default email subject line:</Text>
      <TextInput
        style={[styles.subjectText, { paddingTop: 0, marginTop: Platform.select({ ios: 10, android: 5 }) }]}
        onChangeText={text => actions.setSubject(text)}
        value={state.subject}
        maxLength={100}
        placeholder="Provide a default email subject for followers"
      />
      <Text style={styles.emailBody}>Default email body:</Text>
      <View style={styles.emailBodyTextHolder}>
        <TextInput
          placeholder="Provide a default email or script for followers"
          style={styles.emailBodyText}
          scrollEnabled={false}
          maxLength={2000}
          onChangeText={text => actions.setBody(text)}
          textAlignVertical="top"
          multiline
          value={state.body}
        />
      </View>
    </View>
  )
}

export default Email
