import { StyleSheet } from 'react-native'
import { medium } from '../../../Utils/sizes'
import { darkModeGray, unifiedBlue, unifiedGreen } from '../../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      emailContainer: {
        flexGrow: 1,
        marginBottom: 32,
      },
      subject: {
        fontWeight: medium,
        color: monochroma,
        fontSize: 14,
        marginTop: 22,
      },
      subjectText: {
        color: monochroma,
        fontSize: 14,
        marginTop: 10,
        paddingBottom: 7,
        borderBottomColor: '#b8b8b8',
        borderBottomWidth: 1,
      },
      emailBody: {
        fontWeight: medium,
        color: monochroma,
        fontSize: 14,
        marginTop: 22,
      },
      emailBodyTextHolder: {
        marginTop: 12,
        padding: 12,
        paddingTop: 5,
        borderColor: '#b8b8b8',
        borderWidth: 1,
        borderRadius: 4,
        // backgroundColor: 'blue',
      },
      emailBodyText: {
        color: monochroma,
        // backgroundColor: 'orange',
        fontSize: 14,
        minHeight: 180,
      },
      sendButton: {
        backgroundColor: unifiedBlue,
        borderRadius: 20,
        marginTop: 24,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
      },
      sentButton: {
        backgroundColor: unifiedGreen,
        borderRadius: 20,
        marginTop: 24,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
      },
      sendButtonText: {
        fontWeight: medium,
        color: 'white',
        fontSize: 14,
      },
    })
  }
}
