import { StyleSheet } from 'react-native'
import { unifiedBlue } from '../../../Utils/colors'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      selectButton: {
        color: unifiedBlue,
        fontSize: 14,
      },
      bold: {
        fontSize: 14,
        color: monochroma,
        fontWeight: medium,
      },
      listItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 10,
        paddingBottom: 6,
      },
      listItemText: {
        fontSize: 14,
        flex: 1,
        color: monochroma,
      },
    })
  }
}
