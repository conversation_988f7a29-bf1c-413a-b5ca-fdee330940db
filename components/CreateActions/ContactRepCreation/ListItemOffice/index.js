import React from 'react'
import StyleSheetFactory from './styles'
import { Text, useColorScheme, TouchableOpacity } from 'react-native'
import { useContactRep } from '../LocalStore'

const ListItemOffice = ({ closeMenu, obj = {}, search = '', repText = '', text }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  let tail = repText.slice(search.length)

  //State
  const [state, actions] = useContactRep()

  if (!repText.match(search)) {
    tail = repText
    search = ''
  }

  const handleOnPress = () => {
    actions.setTab('office')
    actions.setSelectedOffice(obj)
    closeMenu()
  }

  return (
    <TouchableOpacity onPress={handleOnPress} style={styles.listItem}>
      <Text numberOfLines={1} style={styles.listItemText}>
        <Text style={styles.bold}>{search}</Text>
        {tail}
        {text}
      </Text>
      <Text style={styles.selectButton}>Select</Text>
    </TouchableOpacity>
  )
}

export default ListItemOffice
