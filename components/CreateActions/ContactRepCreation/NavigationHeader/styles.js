import { Dimensions, StyleSheet } from 'react-native'
import { unifiedBlue, unifiedLightGray, unifiedRoyalBlue } from '../../../Utils/colors'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

    return StyleSheet.create({
      main: {
        maxWidth: '100%',
        height: '100%',
        minHeight: 35,
        width: Dimensions.get('window').width - 60,
        backgroundColor: monochroma2,
        flexDirection: 'row',
        paddingRight: 20,
        alignItems: 'center',
        justifyContent: 'space-between',
      },
      leftSide: {
        backgroundColor: 'red',
        flexDirection: 'row',
        alignItems: 'center',
      },
      leftSideText: {
        fontSize: 16,
        fontWeight: medium,
        color: monochroma,
      },
      publish: {
        fontSize: 16,
        fontWeight: medium,
        color: unifiedBlue,
      },
      publishD: {
        fontSize: 16,
        fontWeight: medium,
        color: unifiedLightGray,
      },
    })
  }
}
