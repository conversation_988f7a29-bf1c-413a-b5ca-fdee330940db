import React, { useContext, useState } from 'react'
import debounce from 'lodash.debounce'
import StyleSheetFactory from './styles'
import { useContactRep } from '../LocalStore'
import { Navigation } from 'react-native-navigation'
import { useGlobalState } from '../../../../contexts/store'
import { View, Text, useColorScheme, Alert, TouchableOpacity, ActivityIndicator, PixelRatio } from 'react-native'
import { useCreatePostState } from '../../../CreatePost/LocalStore'
import { useProfileState } from '../../../Profile/LocalStore'
import { useHomeFeedState } from '../../../Home/LocalStore'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const NavigationHeader = ({ componentId, popTo, onTheFly = false }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const fontScale = PixelRatio.getFontScale()
  const isFontScaled = fontScale === 1.353 // 1.353 is the scale to change design

  //States
  const [state, actions] = useContactRep()
  const [postState, postActions] = useCreatePostState()
  const [globalState, globalActions] = useGlobalState()
  const [isPublishing, setIsPublishing] = useState(false)
  const [profileState, profileActions] = useProfileState()
  const [homeFeedState, homeFeedActions] = useHomeFeedState()

  const assembleFilter = () => {
    const { tab, selectedOffice, selectedOfficial } = state
    let filter = null
    console.log('FILTER OFFICE', selectedOffice, selectedOfficial)
    if (selectedOffice || selectedOfficial) {
      filter = []
      if (tab === 'rep' && selectedOfficial) {
        filter = { official_id: [selectedOfficial.public_official_id] }
      } else if (tab === 'office' && selectedOffice) {
        filter = {
          office_info: [
            {
              display_title: selectedOffice.display_title,
              levels: selectedOffice?.levels,
              roles: selectedOffice?.roles,
              variants: selectedOffice?.variants,
            },
          ],
        }
      }
    }
    return filter
  }

  const publish = async () => {
    if (isPublishing) {
      return
    }
    await setIsPublishing(true)
    try {
      const { say, subject, body } = state
      const title = say.substring(0, 100)
      const filter = assembleFilter()
      const orgId = state.currentPoster ? state.currentPoster.account.id : null
      const result = await globalState.api.actions.createAction(title, say, new Date(), 2, null, filter, subject, body, orgId)
      if (onTheFly) {
        postActions.addAction(result)
        Navigation.popTo(popTo)
        actions.reset()
        return
      }
      await globalState.api.posts.createPost(' ', ' ', [{ content_type: 'action', object_id: result.id, order: 0 }], null, orgId)
      // globalActions.setLastAction(result)
      await setIsPublishing(false)
      profileActions.setReloadKey()
      homeFeedActions.setReloadKey()
      Navigation.popTo(popTo)
      actions.reset()
    } catch (e) {
      await setIsPublishing(false)
      let error = 'Please try again.'
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e?.info?.responseBody?.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else {
          error = e?.info?.responseBody?.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
    }
  }
  const { setToasts, toastRef } = useContext(ToastContext)

  const isReady = () => {
    const { say, subject, body } = state
    const filter = assembleFilter()
    return say.trim() !== '' && subject.trim() !== '' && body.trim() !== '' && filter
  }

  return (
    <View style={styles.main}>
      <Text style={styles.leftSideText}>{isFontScaled ? 'New action' : 'New contact rep action'}</Text>
      {!isPublishing && (
        <TouchableOpacity
          disabled={!isReady()}
          onPress={() => {
            console.log('PRESSED')
            if (isReady()) {
              publish()
            }
          }}>
          <Text style={isReady() ? styles.publish : styles.publishD}>Publish</Text>
        </TouchableOpacity>
      )}
      {isPublishing && <ActivityIndicator size={'small'} color={monochroma} />}
    </View>
  )
}

export default NavigationHeader
