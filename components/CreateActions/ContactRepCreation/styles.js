import { StyleSheet } from 'react-native'
import { unifiedDarkGray, unifiedLightGray } from '../../Utils/colors'
import { medium } from '../../Utils/sizes'
import { unifiedRoyalBlue } from '../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = !isDarkMode ? 'white' : unifiedRoyalBlue

    return StyleSheet.create({
      info: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 5,
      },
      selectRep: {
        fontSize: 14,
        marginRight: 8,
        color: monochroma,
        fontWeight: medium,
      },
      noMatchingContacts: {
        width: '100%',
        textAlign: 'center',
        fontSize: 14,
        color: monochroma,
      },
      form: {
        backgroundColor: 'transparent',
        paddingLeft: 20,
        paddingRight: 20,
        paddingTop: 22,
      },
      title: {
        fontSize: 20,
        fontWeight: medium,
        color: monochroma,
        marginBottom: 6,
      },
      date: {
        fontSize: 12,
        color: unifiedDarkGray,
        marginBottom: 16,
      },
      by: {
        flexDirection: 'row',
        marginBottom: 16,
      },
      picture: {
        height: 34,
        width: 34,
        borderRadius: 999,
      },
      name: {
        fontSize: 16,
        color: monochroma,
        fontWeight: medium,
        marginLeft: 8,
      },
      say: {
        height: 169,
        fontSize: 14,
        lineHeight: 20,
        maxHeight: 169,
        maxWidth: '100%',
        marginBottom: 5,
        color: monochroma,
      },
      counter: {
        fontSize: 10,
        color: monochroma,
        width: '100%',
        textAlign: 'right',
        marginBottom: 22,
      },
      counterGray: {
        fontSize: 10,
        marginBottom: 22,
        color: unifiedLightGray,
        width: '100%',
        textAlign: 'right',
      },
      editContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        // marginBottom: 22,
      },
      edit: {
        color: 'black',
        fontSize: 14,
        marginRight: 8,
        maxWidth: '70%',
      },
      filter: {
        fontSize: 14,
        marginBottom: 6,
        color: monochroma,
      },
      filterBold: {
        fontSize: 14,
        marginBottom: 6,
        color: monochroma,
        fontWeight: medium,
      },
    })
  }
}
