import { Platform, StatusBar, StyleSheet } from 'react-native'
import { unifiedLightGray, unifiedRoyalBlue } from '../../../Utils/colors'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

    return StyleSheet.create({
      //TABS
      tabText: {
        color: monochroma,
      },
      tabBar: {
        flexDirection: 'row',
        backgroundColor: monochroma2,
        // paddingTop: StatusBar.currentHeight,
      },
      tabItem: {
        // flex: 1,
        alignItems: 'center',
        padding: 10,
        fontSize: 14,
      },
      //END TABS
      container: {
        paddingTop: 22,
        paddingLeft: 22,
        paddingRight: 22,
        flex: 1,
      },
      filter: {
        color: monochroma,
        fontSize: 12,
        marginBottom: 32,
        marginTop: 30,
      },
      tabContainer: {
        marginTop: 30,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 32,
      },
      firstTab: {
        alignItems: 'center',
        marginRight: 23,
      },
      areaCode: {
        marginTop: 8,
        color: monochroma,
        fontSize: 12,
        fontWeight: medium,
      },
      areaCodeD: {
        marginTop: 8,
        color: unifiedLightGray,
        fontSize: 12,
        fontWeight: medium,
      },
      secondTab: {
        alignItems: 'center',
        marginLeft: 23,
      },
      registrationStatus: {
        color: monochroma,
        marginTop: 8,
        fontSize: 12,
        fontWeight: medium,
      },
      registrationStatusD: {
        marginTop: 8,
        color: unifiedLightGray,
        fontSize: 12,
        fontWeight: medium,
      },
      followers: {
        color: monochroma,
        fontSize: 14,
        lineHeight: 20,
        marginBottom: 20,
      },
      searchContainer: {
        width: '100%',
        backgroundColor: '#F5F5F5',
        flexDirection: 'row',
        paddingLeft: 12,
        paddingRight: 12,
        borderRadius: 4,
        height: 40,
        alignItems: 'center',
        marginBottom: 20,
      },
      search: {
        color: 'black',
        flex: 1,
        paddingRight: 10,
        fontSize: 16,
      },
    })
  }
}
