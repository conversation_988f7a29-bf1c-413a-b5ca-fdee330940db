/* eslint-disable react-native/no-inline-styles */
/* eslint-disable operator-linebreak */
/* eslint-disable react-hooks/exhaustive-deps */
import ListItemRep from '../ListItemRep'
import StyleSheetFactory from './styles'
import { useContactRep } from '../LocalStore'
import ListItemOffice from '../ListItemOffice'
import FullScreenSheet from '../FullScreenSheet'
import React, { useContext, useEffect, useState } from 'react'
import { unifiedLightGray } from '../../../Utils/colors'
import { useGlobalState } from '../../../../contexts/store'
import { faSearch, faUser, faPodium } from '@fortawesome/pro-regular-svg-icons'
import { faUser as userSolid, faPodium as podiumSolid } from '@fortawesome/pro-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { View, Text, useColorScheme, TouchableOpacity, TextInput, Alert, useWindowDimensions, Animated } from 'react-native'
import { SceneMap, TabView } from 'react-native-tab-view'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const Content = ({ visible, setVisible, closeMenu, type = '' }) => {
  console.log('TYPE', type)
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochromaS = isDarkMode ? 'white' : 'black'
  const monochromaD = unifiedLightGray

  //Global State
  const [globalState] = useGlobalState()
  const [state, actions] = useContactRep()
  const { tab } = state
  const { setTab } = actions

  const [officials, setOfficials] = useState([])
  const [offices, setOffices] = useState([])
  const [search, setSearch] = useState('')

  const loadOfficials = async rawSearch => {
    console.log('SEARCH', rawSearch)
    if (rawSearch.trim() === '' || !rawSearch.trim()) {
      setOfficials([])
      return
    }
    try {
      const result = await globalState.api.representatives.getOfficials(rawSearch)
      console.log('OFFCIALS', JSON.stringify(result))
      const slicedArray = result.slice(0, 10)
      setOfficials(slicedArray)
    } catch (e) {
      console.log('REP ERROR', JSON.stringify(e))
      let error = 'Please try again.'
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e?.info?.responseBody?.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else {
          error = e?.info?.responseBody?.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
    }
  }
  const { setToasts, toastRef } = useContext(ToastContext)

  const loadOffices = async rawSearch => {
    try {
      console.log('SEARCH', rawSearch)
      const result = await globalState.api.representatives.getOffices(rawSearch)
      console.log('OFFICES', rawSearch, result)
      const slicedArray = result.slice(0, 10)
      setOffices(slicedArray)
    } catch (e) {
      let error = 'Please try again.'
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e?.info?.responseBody?.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else {
          error = e?.info?.responseBody?.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
    }
  }

  const [timer, setTimer] = useState(null)

  const handleOnSearch = text => {
    setSearch(text)

    clearTimeout(timer)

    const newTimer = setTimeout(() => {
      if (type === 'rep') {
        loadOfficials(text)
      } else {
        loadOffices(text)
      }
    }, 500)

    setTimer(newTimer)
  }

  const handleActionPress = () => {
    setVisible(true)
  }

  const handleTabSelection = async rawTab => {
    await setSearch('')
    await setOffices([])
    await setOfficials([])
    await setTab(rawTab)
  }

  useEffect(() => {
    if (type === 'office') {
      loadOffices('')
    }
  }, [type])

  return (
    <View style={styles.container}>
      <Text style={styles.followers}>
        {type === 'rep'
          ? 'Pick a representative by name. Everyone taking action will contact the same representative.'
          : 'Pick the office type to contact. Each person taking action will contact their own rep for that office.'}
      </Text>
      <View style={styles.searchContainer}>
        <TextInput value={search} onChangeText={text => handleOnSearch(text)} placeholderTextColor={unifiedLightGray} placeholder="Search" style={styles.search} />
        <FontAwesomeIcon color={monochromaS} size={22} icon={faSearch} />
      </View>
      {type === 'rep' &&
        officials.map((e, i) => {
          return <ListItemRep closeMenu={closeMenu} setVisible={setVisible} obj={e} key={`r-${i}`} text={`${e.name}: ${e.title}`} />
        })}

      {type === 'office' &&
        offices.map((e, i) => {
          return <ListItemOffice closeMenu={closeMenu} setVisible={setVisible} obj={e} key={`o-${i}`} text={e.display_title} />
        })}
    </View>
  )
}

const SelectRepresentativeCore = ({ visible, setVisible, closeMenu }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const monochroma2 = unifiedLightGray

  const layout = useWindowDimensions()

  const [index, setIndex] = React.useState(0)
  const [routes] = React.useState([
    { key: 'representatives', title: 'Representatives' },
    { key: 'offices', title: 'Offices' },
  ])

  const RepresentativesWrap = () => <Content type="rep" closeMenu={closeMenu} visible={visible} setVisible={setVisible} />

  const OfficeWrap = () => <Content type="office" closeMenu={closeMenu} rawTab="office" visible={visible} setVisible={setVisible} />

  const renderScene = SceneMap({
    representatives: RepresentativesWrap,
    offices: OfficeWrap,
  })

  const [state, actions] = useContactRep()
  const { tab } = state
  const { setTab } = actions

  const renderTabBar = props => {
    const inputRange = props.navigationState.routes.map((x, i) => i)

    return (
      <View style={styles.tabBar}>
        <View style={{ backgroundColor: unifiedLightGray, height: 2, width: '100%', position: 'absolute', bottom: 0 }} />
        {props.navigationState.routes.map((route, i) => {
          const opacity = props.position.interpolate({
            inputRange,
            outputRange: inputRange.map(inputIndex => (inputIndex === i ? 1 : 0.5)),
          })
          let styleBox
          if (index === i) {
            styleBox = { ...styles.tabItem, borderBottomColor: '#CA09BC', borderBottomWidth: 2 }
          } else {
            styleBox = { ...styles.tabItem }
          }
          if (i === 0) {
            styleBox.marginLeft = 10
          }
          if (i === 1) {
            styleBox.marginLeft = 30
          }
          const getIcon = () => {
            if (route.title === 'Representatives') {
              if (index === i) {
                return userSolid
              } else {
                return faUser
              }
            } else if (route.title === 'Offices') {
              if (index === i) {
                return podiumSolid
              } else {
                return faPodium
              }
            }
          }
          return (
            <TouchableOpacity
              key={`tab-${i}`}
              style={styleBox}
              onPress={() => {
                if (i === 0) {
                  setTab('rep')
                } else {
                  setTab('office')
                }
                setIndex(i)
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <FontAwesomeIcon icon={getIcon()} color={monochroma} size={25} />
                <Animated.Text style={[styles.tabText, { opacity }, { marginLeft: 8 }]} numberOfLines={1}>
                  {route.title}
                </Animated.Text>
              </View>
            </TouchableOpacity>
          )
        })}
      </View>
    )
  }

  return (
    <View style={{ flex: 1, backgroundColor: 'transparent', height: 600, paddingTop: 22 }}>
      <TabView renderTabBar={renderTabBar} navigationState={{ index, routes }} renderScene={renderScene} onIndexChange={setIndex} initialLayout={{ width: layout.width }} />
    </View>
  )
}

const SelectRepresentative = ({ visible, setVisible }) => {
  return (
    <FullScreenSheet visible={visible} setVisible={setVisible}>
      <SelectRepresentativeCore visible={visible} setVisible={setVisible} />
    </FullScreenSheet>
  )
}

export default SelectRepresentative
