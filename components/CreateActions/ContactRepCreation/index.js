/* eslint-disable react-native/no-inline-styles */
import dayjs from 'dayjs'
import Email from './Email'
import React, { useEffect, useRef, useState } from 'react'
import StyleSheetFactory from './styles'
import { useContactRep } from './LocalStore'
import FormWrapper from '../../Wrappers/FormWrapper'
import { useGlobalState } from '../../../contexts/store'
import SelectRepresentative from './SelectRepresentative'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { faPen, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons'
import { unifiedBlue, unifiedDarkGray, unifiedLightGray, unifiedRoyalBlue } from '../../Utils/colors'
import { View, Text, useColorScheme, TextInput, Image, TouchableOpacity, Pressable } from 'react-native'
import CreatorActionSheet from '../../CreatorActionSheet'

const ContactRepCreation = () => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  //States
  const [globalState] = useGlobalState()
  const [state, actions] = useContactRep()
  const [visible, setVisible] = useState(false)
  const { profile } = globalState
  const { display_name: dn, profile_pic: picture } = profile

  //Refs
  const sayRef = useRef()
  const actionSheetRef = useRef(null)

  //Effects
  useEffect(() => {
    setTimeout(() => sayRef.current.focus(), 250)
  }, [])

  const handleActionPress = () => {
    setVisible(true)
  }

  return (
    <>
      <FormWrapper style={{ backgroundColor: isDarkMode ? unifiedRoyalBlue : 'white' }}>
        <View style={styles.form}>
          {/* <TextInput multiline maxLength={70} value={state.title} onChangeText={text => actions.setTitle(text)} placeholder="Title" placeholderTextColor={unifiedDarkGray} style={styles.title} />
        <Text style={styles.date}>{dayjs().format('MMMM D, YYYY')}</Text> */}
          <Pressable onLongPress={() => actionSheetRef.current.show()} style={[styles.by, { alignItems: 'center', marginBottom: 16 }]}>
            {state.currentPoster ? (
              <>
                <Image style={styles.picture} source={state.currentPoster.profile_pic ? state.currentPoster.profile_pic : require('../../../assets/images/noUser.png')} />
                <View>
                  <Text style={styles.name}>{state.currentPoster.display_name}</Text>
                  {state.currentPoster && <Text style={{ color: monochroma, fontSize: 10, marginLeft: 10 }}>from {dn}</Text>}
                </View>
              </>
            ) : (
              <>
                <Image style={styles.picture} source={picture ? { uri: picture.uri } : require('../../../assets/images/noUser.png')} />
                <Text style={styles.name}>{dn}</Text>
              </>
            )}
          </Pressable>
          <TextInput
            ref={sayRef}
            multiline
            maxLength={320}
            numberOfLines={8}
            value={state.say}
            style={styles.say}
            textAlignVertical="top"
            placeholder="What do you want to say?"
            placeholderTextColor={unifiedDarkGray}
            onChangeText={text => actions.setSay(text)}
          />
          <Text style={state.say.length === 0 ? styles.counterGray : styles.counter}>{`${state.say.length}/320`}</Text>
          <TouchableOpacity style={styles.info}>
            <Text style={styles.selectRep}>Representative filter</Text>
          </TouchableOpacity>
          <Text style={{ fontSize: 12, lineHeight: 16, color: '#8d8d8d', marginBottom: 5 }}>
            Use this filter to ask followers to contact a specific elected official or any official holding a particular office.
          </Text>
          <TouchableOpacity
            onPress={handleActionPress}
            style={[styles.editContainer, { backgroundColor: state.selectedOffice || state.selectedOfficial ? '#e8fbf2' : '#ffe8f3', padding: 16, borderRadius: 8 }]}>
            {!state.selectedOffice && !state.selectedOfficial && <Text style={styles.edit}>No representative selected</Text>}
            {state.tab === 'rep' && state.selectedOfficial && (
              <Text style={styles.edit} numberOfLines={1}>
                {state.selectedOfficial.name} ({state.selectedOfficial.title})
              </Text>
            )}
            {state.tab === 'office' && state.selectedOffice && <Text style={styles.edit}>{state.selectedOffice.display_title}</Text>}
            <FontAwesomeIcon color={'black'} size={16} icon={faPen} />
          </TouchableOpacity>
          <Email />
        </View>

        {visible && <SelectRepresentative visible={visible} setVisible={setVisible} />}
      </FormWrapper>
      <CreatorActionSheet
        displayName={dn}
        profile={profile}
        picture={picture}
        scopes={['create_action']}
        actionSheetRef={actionSheetRef}
        currentPoster={state.currentPoster}
        setCurrentPoster={actions.setCurrentPoster}
      />
    </>
  )
}

export default ContactRepCreation
