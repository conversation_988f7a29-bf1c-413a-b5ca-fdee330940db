
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import DevMenu from './DevMenu'
import StyleSheetFactory from './styles'
import GenericHeader from '../GenericHeader'
import LargeInput from '../Inputs/LargeInput'
import LargeButton from '../Inputs/LargeButton'
import FormWrapper from '../Wrappers/FormWrapper'
import DeviceInfo from 'react-native-device-info'
import { Navigation } from 'react-native-navigation'
import { useGlobalState } from '../../contexts/store'
import MainScreenOption from '../Inputs/MainScreenOption'
import SequentialTapWrapper from '../SequentialTapWrapper'
import React, { useContext, useEffect, useRef, useState } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { faArrowRightToBracket, faKey } from '@fortawesome/pro-solid-svg-icons'
import { Linking, Platform, Text, useColorScheme, View, PixelRatio, Alert, ActivityIndicator, ImageBackground } from 'react-native'
import { goCreateProfile } from '../../navigation'
import { ToastContext } from '../Home/ToastStackWrapper'
import { useCreateProfileState } from '../CreateProfile/LocalStore'
import { PP_URL, TOU_URL, WAITLIST_URL } from '../../constants'

const background = require('../../assets/images/background.png')

import {
  BatchSize,
  DatadogProvider,
  DatadogProviderConfiguration,
  UploadFrequency,
} from '@datadog/mobile-react-native'


const config = new DatadogProviderConfiguration(
  'pub889a4a899919a87294cbde798887aa70',
  __DEV__ ? 'dev' : 'prod', // Environment name
  '6036c4d2-d9f9-42a6-b590-4c7342311787',
  true, // track User interactions (e.g.: Tap on buttons. You can use 'accessibilityLabel' element property to give tap action the name, otherwise element type will be reported)
  true, // track XHR Resources
  true // track Errors
)
// Optional: Select your Datadog website (one of "US1", "EU1", "US3", "US5", "AP1" or "GOV")
config.site = 'US1'
// Optional: Enable JavaScript long task collection
config.longTaskThresholdMs = 100
// Optional: enable or disable native crash reports
config.nativeCrashReportEnabled = true
// Optional: sample RUM sessions (here, 100% of session will be sent to Datadog. Default = 100%. Only tracked sessions send RUM events.)
config.sessionSamplingRate = 100

if (__DEV__) {
  // Optional: Send data more frequently
  config.uploadFrequency = UploadFrequency.FREQUENT
  // Optional: Send smaller batches of data
  config.batchSize = BatchSize.SMALL
}


const Main = ({ componentId }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const fontScale = PixelRatio.getFontScale()
  const isFontScaled = fontScale === 1.353 // 1.353 is the scale to change design

  // Regex
  const emailExp = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  const phoneExp = /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/

  //Local State
  const [dev, setDev] = useState(false)
  const [local, setLocal] = useState('')
  const [deviceInfoSent, setDeviceInfoSent] = useState(false)
  const [host, setHost] = useState({ prod: true, dev: false, local: false })

  //Global State
  const [globalState, globalActions] = useGlobalState()
  const [createProfileState, createProfileActions] = useCreateProfileState()

  //Context
  const { setToasts, toastRef } = useContext(ToastContext)

  //EXPERIMENTAL CODE ACCESS
  useEffect(() => {
    const updateDev = async () => {
      const value = await AsyncStorage.getItem('EXPERIMENTAL-API')
      console.log('API VAL', value)
      if (value === 'good2go') {
        setDev(true)
      }
    }
    updateDev()
    console.log('CURRENT SCALE', fontScale)
  }, [])


  const getInfo = _ => {
    try {
      const fillPayload = () => {
        const uniqueId = DeviceInfo.getUniqueId()
        const model = DeviceInfo.getModel()
        const deviceId = DeviceInfo.getDeviceId()
        const systemVersion = DeviceInfo.getSystemVersion()
        const appVersion = DeviceInfo.getVersion()
        const sessionId = globalActions.setSessionId()

        return {
          device_info: {
            device: {
              uniqueId,
              model,
              deviceId,
              systemVersion,
              appVersion,
            },
            event: 'user_signup',
            session_id: sessionId,
          },
        }
      }
      console.log('FILL PAYLOAD', fillPayload())
      globalState.api.logging.logDeviceInfoWithSessionNoAuth(fillPayload())
      globalActions.logEventNoAuth('signup_landing','Main','view','Main/index.js',{})
    } catch (e) {
      console.log('THERE WAS AN ERROR SENDING THE DEVICE INFO', e)
    }
  }

  useEffect(() => {
    console.log('%cSTARTING THE APP', 'color: teal; font-weight: bold;', globalState.api.logging)
    if (!deviceInfoSent && globalState.api.logging) {
      setDeviceInfoSent(true)
      console.log('SENDING DEVICE INFO FIRST TIME')
      getInfo()
    }
  }, [globalState.api,globalState.api.logging, deviceInfoSent])

  const toggleEnvironment = async newHost => {
    await globalActions.resetAuth()
    if (newHost.dev) {
      console.log('Dev')
      await AsyncStorage.setItem('APIURL', 'https://api.dev.unified.community/v1')
    } else if (newHost.local) {
      console.log('LOCAL')
      await AsyncStorage.setItem('APIURL', local)
    } else {
      console.log('PROD')
      await AsyncStorage.setItem('APIURL', 'https://api.prod.unified.community/v1')
    }
    await globalActions.resetAPI()
    setTimeout(() => {
      setDeviceInfoSent(false)
    }, 1000)
  }

  const handleSwitch = async env => {
    const newHost = JSON.parse(JSON.stringify({ ...host }))
    Object.keys(newHost).forEach(e => {
      if (e === env) {
        newHost[e] = true
      } else {
        newHost[e] = false
      }
    })
    await setHost(newHost)
    await toggleEnvironment(newHost)
  }

  const goToLogin = () => {
    Navigation.push(componentId, {
      component: {
        name: 'Login',
        passProps: {
          background,
        },
        options: {
          animations: {
            push: {
              waitForRender: true,
            },
          },
          bottomTabs: {
            visible: false,
            drawBehind: true,
          },
          topBar: {
            visible: false,
            animate: false,
          },
        },
      },
    })
  }

  const gotoTerms = () => Linking.openURL(TOU_URL)
  const gotoPrivacy = () => Linking.openURL(PP_URL)


  // Sign Up
  const phoneEmailRef = useRef(null)
  const inviteCodeRef = useRef(null)
  const [isFocused, setFocus] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [phoneEmail, setPhoneEmail] = useState('')
  const [inviteCode, setInviteCode] = useState('')
  const [syntaxError, setSyntaxError] = useState(false)
  const handlePhoneEmail = e => setPhoneEmail(e.trim())
  const handleInviteCode = e => setInviteCode(e.trim())
  const [whiteListError, setWhiteListError] = useState(false)
  const [existError, setExistError] = useState(false)
  const [inviteCodeError, setInviteCodeError] = useState(false)

  //Checks if email and phoneEmail have valid values
  const checkForm = () => !!phoneEmail.trim()

  const handleSubmit = () => {
    createProfileActions.reset()
    if (emailExp.test(phoneEmail) || phoneExp.test(phoneEmail)) {
      if (checkForm()) {
        setLoading(true)
      }
    }
  }

  const sendConfirmationData = async (email = '', phone = '') => {
    setWhiteListError(false)
    setInviteCodeError(false)
    setSyntaxError(false)
    setExistError(false)
    try {
      globalActions.logEventNoAuth('create_account','Main','tap','Main/index.js',{email, phone, invite_code:inviteCode})
      const result = await globalState.api.startDirectRegistration(email, phone, inviteCode) // inviteCode can be anything
      console.log('SIGN UP SUCCESS', result)
      goCreateProfile(result.workflow_id, phoneEmail)
      setFocus(false)
      setLoading(false)
    } catch (e) {
      console.log('SIGN UP ERROR', JSON.stringify(e))
      let error = 'Please try again.'
      setFocus(false)
      setLoading(false)
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e.info.responseBody.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else if (typeof e.info.responseBody.detail === 'string') {
          error = e.info.responseBody.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
    }
  }

  const trySignUp = async () => {
    const isValidEmail = emailExp.test(phoneEmail)
    const isValidPhone = phoneExp.test(phoneEmail)
    console.log('RUNNING')
    if (isValidEmail) {
      setSyntaxError(false)
      await sendConfirmationData(phoneEmail)
    } else if (isValidPhone) {
      setSyntaxError(false)
      await sendConfirmationData('', phoneEmail)
    } else {
      setFocus(false)
      setLoading(false)
      setSyntaxError(true)
      // Alert.alert('Error', 'Please check that the information is correct and try again. Thank you!')
      return
    }
  }

  useEffect(() => {
    if (isLoading) {
      setTimeout(trySignUp, 10) // change back to 500 when Sign Up logic is implemented
    }
  }, [isLoading])

  const isButtonInactive = () => {
    if(!phoneEmail.trim() || !inviteCode.trim()) {
      return true
    }
    if(inviteCode.trim() === '') {
      return true
    }
    return false
  }

  return (
    <ImageBackground source={require('../../assets/images/background.png')} style={{ flex: 1, height: '100%' }}>
      <DatadogProvider configuration={config}>
        <FormWrapper style={styles.main} androidBG enableAutomaticScroll={true} extraScrollHeight={120}>
          <GenericHeader />
          <View style={{ backgroundColor: 'transparent', marginTop: isFontScaled ? 300 : Platform.select({ ios: 360, android: 409 }) }}>
            {!isLoading && (<SequentialTapWrapper requiredTaps={5} tapThreshold={1000} onSequentialTap={() => setDev(!dev)}>
              <Text style={{ color: 'white', fontSize: 20, marginLeft: 30, fontWeight: '500' }}>Create account</Text>
            </SequentialTapWrapper>)}
            {!isLoading && (
              <View style={{ marginHorizontal: 30, marginTop: 16, backgroundColor:'transparent' }}>
                <View style={{ marginBottom: 8 }}>
                  <LargeInput
                    placeholderTextColor="white"
                    placeholder="Email or phone"
                    value={phoneEmail.toLowerCase()}
                    ref={phoneEmailRef}
                    returnKeyType="go"
                    autoCapitalize="none"
                    onBlur={() => setFocus(false)}
                    onFocus={() => setFocus(true)}
                    onChangeText={val => handlePhoneEmail(val)}
                  />
                </View>
                <View style={{ marginBottom: 8 }}>
                  <LargeInput
                    placeholderTextColor="white"
                    placeholder="Invite code"
                    value={inviteCode}
                    ref={inviteCodeRef}
                    returnKeyType="go"
                    autoCapitalize="none"
                    onSubmitEditing={handleSubmit}
                    onBlur={() => setFocus(false)}
                    onFocus={() => setFocus(true)}
                    onChangeText={val => handleInviteCode(val)}
                  />
                </View>
                <LargeButton onPress={handleSubmit} isInactive={isButtonInactive()} text="Continue"/>
                <View style={{ marginTop: 40 }}>
                  <MainScreenOption icon={faKey} onPress={()=> {
                    globalActions.logEventNoAuth('request_invite_code','Main','tap','Main/index.js',{})
                    Linking.openURL(WAITLIST_URL)
                  }
                  } text="Request an invite code" />
                </View>
                <View style={{ marginTop: 16 }}>
                  <MainScreenOption icon={faArrowRightToBracket} onPress={()=> goToLogin()} text="Login" />
                </View>
                <Text style={{marginTop:Platform.select({ios:40, android:24}), color:'white', fontSize:12, fontWeight:'400'}}>
              By creating an account or logging in, you are agreeing to our <Text onPress={gotoTerms} style={{ textDecorationLine: 'underline' }}>Terms of Use</Text> and <Text onPress={gotoPrivacy} style={{ textDecorationLine: 'underline' }}>Privacy Policy</Text>.
                </Text>
              </View>
            )}
          </View>
          {isLoading && (
            <View style={{marginTop:16}}>
              <ActivityIndicator size="large" color="white" />
            </View>
          )}
          {Platform.OS === 'android' && isFontScaled && <View style={{ height: 100 }} />}
          {true && <DevMenu host={host} handleSwitch={handleSwitch} />}
        </FormWrapper>
      </DatadogProvider>
    </ImageBackground>
  )
}

export default Main
