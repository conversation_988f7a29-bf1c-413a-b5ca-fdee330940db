/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import dayjs from 'dayjs'
import StyleSheetFactory from './styles'
import { executeNavigation } from './navigation'
import React, { useEffect, useState } from 'react'
import ProgressBar from 'react-native-progress/Bar'
import { useVoterRegActionState } from '../LocalStore'
import { faRegistered } from '@fortawesome/pro-solid-svg-icons'
import { faEllipsisV } from '@fortawesome/pro-regular-svg-icons'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { View, Text, useColorScheme, Image, TouchableOpacity, Pressable } from 'react-native'
import { getFormat } from '../../../Utils/dateFormat'
import SocialBlurp from '../../../SocialBlurp'
import { useGlobalState } from '../../../../contexts/store'

const Header = ({ item, step, contact = {}, componentId, optionsRef }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const monochroma2 = isDarkMode ? 'black' : 'white'

  // Global State
  const [globalState] = useGlobalState()

  // Local States
  const [ownPost, setOwnPost] = useState(false)
  const [locationD, setLocationD] = useState('')
  const [completors, setCompletors] = useState([])
  const [isNavigating, setNavigating] = useState(false)
  const [localState, localActions] = useVoterRegActionState()

  const { description, title, start_date: date, creator, contact_filters = {} } = item
  const { display_name: displayName, profile_pic: picture } = creator
  const { given_name: contactFN = '', family_name: contactLN = '' } = contact

  console.log('CONTACT FILTER', contact_filters)
  useEffect(() => {
    const { family_name, given_name, postal_addresses = [] } = contact
    let location = ''
    if (postal_addresses.length > 0) {
      const { city: contactCity = '', postal_code: postal = '', state: contactState = '', street = '' } = postal_addresses[0]
      if (contactCity) {
        location = contactCity
      }
      if (contactState) {
        if (contactCity) {
          location = `${location}, ${contactState}`
        } else {
          location = contactState
        }
      }
    } else {
      location = 'Unknown location'
    }
    setLocationD(location)
  }, [contact])

  useEffect(() => {
    if (step === 0) {
      console.log('THERE------------')
      getCompletors()
    }
  }, [step])

  // Use Effects
  const getCompletors = async () => {
    // Global State
    const response = await globalState.api.actions.getActionsCompletors(item.id)

    try {
      // console.log('COMPLETORS LL', response)
      setCompletors(response)
    } catch (e) {
      console.log('PROFILE ERROR LL', JSON.stringify(e))
      setCompletors([])
    }
  }

  useEffect(() => {
    console.log('ITEM VOTER REG', item.id)
    getCompletors()
  }, [])

  const generateText = () => {
    const { area_code = [], voter_reg_status = [] } = contact_filters

    if (area_code && area_code.length > 0) {
      const extra = []
      let tail = ''
      if (area_code.length > 1) {
        area_code.forEach((e, i) => {
          if (i !== 0) {
            extra.push(e)
          }
        })
        tail = ` or ${extra.join(' or ')}`
        console.log('TAIL', tail)
      }
      return (
        <>
          <Text style={styles.creamTitle}>A contact filter is active for this action:</Text>
          <Text style={styles.creamDesc}>
            Only contacts with a {area_code[0]}
            {tail} area code in their phone number will be shown.
          </Text>
        </>
      )
    } else {
      const extra = []
      let tail = ''
      if (voter_reg_status.length > 1) {
        voter_reg_status.forEach((e, i) => {
          if (i !== 0) {
            extra.push(e)
          }
        })
        tail = ` or ${extra.join(' or ')}`
      }
      return (
        <>
          <Text style={styles.creamTitle}>A contact filter is active for this action:</Text>
          <Text style={styles.creamDesc}>
            Only contacts that are {voter_reg_status[0]}
            {tail} voters
          </Text>
        </>
      )
    }
  }

  const getFilterStatus = () => {
    const { area_code = [], voter_reg_status = [] } = contact_filters
    return area_code?.length > 0 || voter_reg_status?.length > 0
  }

  const handleNavigationProfile = async action => {
    if (isNavigating) {
      return
    }
    await setNavigating(true)
    await executeNavigation('Profile', componentId, { externalUser: creator }, true)
    await setNavigating(false)
  }

  useEffect(() => {
    const test = async () => {
      let savedProfile = globalState.mmkv.getString('PROFILE')
      savedProfile = JSON.parse(savedProfile)
      if (item.creator.account_id === savedProfile.account.id) {
        setOwnPost(true)
      }
    }
    console.log('ACITON POST', JSON.stringify(item))
    test()
  }, [])

  return (
    <>
      <View style={{ marginTop: 14, marginBottom: 22, marginLeft: 22, marginRight: 22, flexDirection: 'row', backgroundColor: 'transparent', justifyContent: 'space-between' }}>
        <TouchableOpacity onPress={() => handleNavigationProfile(false)} style={styles.namePicture}>
          <Image transition={false} style={styles.picture} source={picture ? { uri: picture.uri } : require('../../../../assets/images/noUser.png')} />

          <Text style={styles.by}>
            {displayName}
            <Text style={[styles.date, { paddingLeft: 10, backgroundColor: 'transparent' }]}>
              {'  '}
              {getFormat(date)}
            </Text>{' '}
          </Text>
        </TouchableOpacity>
        {!ownPost && (
          <Pressable onPress={() => optionsRef?.current?.show()} style={{ backgroundColor: 'transparent', alignItems: 'flex-end', justifyContent: 'center', height: 42, width: 60 }}>
            <FontAwesomeIcon icon={faEllipsisV} color={'#8D8D8D'} size={14} />
          </Pressable>
        )}
      </View>
      <Text selectable style={styles.description}>
        {description}
      </Text>
      <View style={{ marginLeft: 22, marginRight: 22, marginTop: 10 }}>
        <SocialBlurp completors={completors} componentId={componentId} totalCompletors={completors.length} />
      </View>
      {step === 0 && (
        <View style={styles.creamSquare}>
          {!getFilterStatus() && (
            <>
              <Text style={styles.creamTitle}>No contact filter is active.</Text>
            </>
          )}
          {getFilterStatus() && generateText()}
        </View>
      )}
      {step !== 0 && (
        <View style={styles.contactMacroContainer}>
          <Text style={styles.checkContact}>Check contact:</Text>
          <ProgressBar borderColor={monochroma2} height={2} color={'#CA09BC'} progress={localState.progress} width={null} />
          <View style={styles.contactHolder}>
            <Image transition={false} style={styles.contactPicture} source={require('../../../../assets/images/noUser.png')} />
            <View>
              <Text style={styles.contactName}>
                {contactFN} {contactLN}
              </Text>
              <Text style={styles.contactTitle}>{locationD}</Text>
            </View>
          </View>
        </View>
      )}
    </>
  )
}

export default Header
