import { StyleSheet } from 'react-native'
import { unifiedDarkGray } from '../../../Utils/colors'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      checkContact: {
        fontSize: 14,
        color: isDarkMode ? unifiedDarkGray : 'black',
        fontWeight: medium,
        marginBottom: 8,
      },
      creamTitle: {
        fontSize: 14,
        color: 'black',
        fontWeight: medium,
      },
      creamDesc: {
        fontSize: 14,
        lineHeight: 18,
        color: 'black',
      },
      creamSquare: {
        marginTop: 22,
        backgroundColor: 'rgb(255,246,232)',
        borderRadius: 8.4,
        marginRight: 22,
        marginLeft: 22,
        padding: 17,
      },
      namePicture: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'transparent',
      },
      by: {
        fontSize: 16,
        fontWeight: medium,
        color: monochroma,
      },
      picture: {
        width: 42,
        height: 42,
        marginRight: 10,
        borderRadius: 999,
      },
      contactRep: {
        paddingTop: 0,
        backgroundColor: monochroma2,
      },
      description: {
        marginLeft: 22,
        marginRight: 22,
        fontSize: 14,
        color: monochroma,
      },
      titleDate: {
        marginLeft: 11,
        flex: 1,
      },
      title: {
        fontSize: 17,
        fontWeight: medium,
        color: monochroma,
        flex: 1,
      },
      date: {
        fontSize: 11,
        color: unifiedDarkGray,
      },
      mainTitle: {
        marginLeft: 0,
        marginRight: 0,
        backgroundColor: 'transparent',
        flexDirection: 'row',
        flex: 1,
      },
      voterRegistration: {
        marginTop: 14,
      },
      contactMacroContainer: {
        marginTop: 22,
        marginLeft: 22,
        marginRight: 22,
      },
      contactHolder: {
        marginTop: 8,
        backgroundColor: isDarkMode ? 'gray' : '#FAFAFA',
        borderRadius: 14,
        padding: 17,
        flexDirection: 'row',
      },
      contactPicture: {
        width: 80,
        height: 80,
        marginRight: 12,
        borderRadius: 200,
      },
      contactDescription: {
        justifyContent: 'center',
      },
      contactName: {
        fontSize: 16,
        fontWeight: medium,
        color: monochroma,
        marginBottom: 4,
      },
      contactTitle: {
        fontSize: 14,
        color: monochroma,
        marginBottom: 6,
      },
    })
  }
}
