/* eslint-disable prettier/prettier */
import { createStore, createHook } from 'react-sweet-state'

const Store = createStore({
  // value of the store on initialisation
  initialState: {
    progress: 0.4,
    response: null,
    matchedData: null,
    contact: {},
    atLeastOne: false,
    flow : null,
    reloadKey:'',
  },
  // actions that trigger store mutation
  actions: {
    // mutate state syncronously
    reset:
      () =>
        ({ setState, getState }) => {
          setState({
            progress: 0.4,
            response: null,
            reloadKey:'',
            matchedData: null,
            flow:null,
            contact: {},
            atLeastOne: false })
        },
    setReloadKey:
      () =>
        ({ setState, getState }) => {
          setState({ reloadKey:Math.random() })
        },
    setProgress:
        (progress = null) =>
          ({ setState, getState }) => {
            setState({ progress })
          },
    setFlow:
        (flow = null) =>
          ({ setState, getState }) => {
            setState({ flow })
          },
    setAtLeastOne:
      (atLeastOne = null) =>
        ({ setState, getState }) => {
          setState({ atLeastOne })
        },
    setContact:
      (contact = null) =>
        ({ setState, getState }) => {
          setState({ contact })
        },
    setMatchedData:
      (matchedData = null) =>
        ({ setState, getState }) => {
          setState({ matchedData })
        },
    setResponse:
      (response = null) =>
        ({ setState, getState }) => {
          setState({ response })
        },
  },
  // optional, mostly used for easy debugging
  name: 'voterRegistrationActionGlobal',
})

export const useVoterRegActionState = createHook(Store)
