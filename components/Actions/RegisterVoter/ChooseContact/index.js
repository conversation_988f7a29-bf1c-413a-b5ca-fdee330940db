/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect } from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, Image, TouchableOpacity, Alert } from 'react-native'
import { useVoterRegActionState } from '../LocalStore'
import { useGlobalState } from '../../../../contexts/store'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { faAngleRight, faCheck } from '@fortawesome/pro-solid-svg-icons'
import { unifiedGreen } from '../../../Utils/colors'
import { ToastContext } from '../../../Home/ToastStackWrapper'
import { useMMKVBoolean } from 'react-native-mmkv'

const ChooseContact = ({ checked = false, setVisible, isVisible = false, setStep, contacts = [], getContacts, activePostId = '', item }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  //Global State
  const [globalState] = useGlobalState()
  //MMKV
  const [confirmed = false] = useMMKVBoolean('IS_CONFIRMED', globalState.mmkv)
  //Local State
  const [localState, localActions] = useVoterRegActionState()
  //Context
  const { showConfirmActionSheet } = useContext(ToastContext)

  useEffect(() => {
    let isComplete = false
    contacts.forEach(e => {
      const { voter_reg_matched, voter_reg_contacted } = e
      if (voter_reg_matched) {
        isComplete = true
      } else if (voter_reg_contacted) {
        isComplete = true
      }
    })
    localActions.setAtLeastOne(isComplete)
  }, [contacts])

  const { setToasts, toastRef } = useContext(ToastContext)

  const updateFlow = async (id, contact) => {
    try {
      const result = await globalState.api.flows.updateFlow(localState.flow.id, localState.flow.state, 'forward', { chosen_contact_id: id })
      console.log('RESULT', result)
      localActions.setFlow(result)
      // getContacts(result.id, result.state) // double check
      localActions.setContact(contact)
      setTimeout(() => setStep(1), 250)
    } catch (e) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'There was a communication error.', type: 'error' }])
      toastRef.current.show()
      console.log('ERROR', JSON.stringify(e))
    }
  }

  const handleOnPress = async contact => {
    if (!confirmed) {
      showConfirmActionSheet('before reaching out to this contact.')
      return
    }

    const { id } = contact

    await updateFlow(id, contact)
  }

  const getButtonText = contact => {
    const { voter_reg_matched, voter_reg_contacted, voter_reg_checked } = contact
    if (voter_reg_matched || voter_reg_contacted || voter_reg_checked) {
      return <FontAwesomeIcon size={22} icon={faCheck} color={unifiedGreen} />
    } else {
      return <FontAwesomeIcon size={22} icon={faAngleRight} color={'black'} />
    }
  }

  const getCheckedNumber = _ => {
    let total = 0
    contacts.forEach(e => {
      const { voter_reg_matched, voter_reg_contacted, voter_reg_checked } = e

      if (voter_reg_matched || voter_reg_contacted || voter_reg_checked) {
        total++
      }
    })
    return total
  }

  return (
    <View>
      <View style={isVisible ? styles.body : { display: 'none' }}>
        {!checked && <Text style={styles.recomended}>Recommended contacts for you:</Text>}
        {contacts.map((e, i) => {
          const { family_name, given_name, postal_addresses = [], voter_reg_matched, voter_reg_contacted, voter_reg_checked } = e
          let location = ''
          if (postal_addresses.length > 0) {
            const { city: contactCity = '', postal_code: postal = '', state: contactState = '', street = '' } = postal_addresses[0]
            if (contactCity) {
              location = contactCity
            }
            if (contactState) {
              if (contactCity) {
                location = `${location}, ${contactState}`
              } else {
                location = contactState
              }
            }
          } else {
            location = '-'
          }

          if (voter_reg_matched || voter_reg_contacted || voter_reg_checked) {
            return null
          }

          return (
            <TouchableOpacity disabled={checked} onPress={() => handleOnPress(e)} key={`person-${i}`} style={checked ? styles.containerChecked : styles.container}>
              <View style={styles.person}>
                <Image transition={false} style={styles.picture} source={require('../../../../assets/images/noUser.png')} />
                <View>
                  <Text style={styles.name}>
                    {given_name} {family_name}
                  </Text>
                  <Text style={styles.location}>{location}</Text>
                </View>
              </View>
              <View style={voter_reg_matched || voter_reg_contacted ? styles.checkDone : styles.check}>{getButtonText(e)}</View>
            </TouchableOpacity>
          )
        })}
      </View>
      <View style={isVisible ? styles.body : { display: 'none' }}>
        <View style={styles.checkedContacts}>
          <Text style={styles.checkedContactsText}>Checked contacts:</Text>
          <Text style={styles.checkedContactsText}>
            {getCheckedNumber()}/{contacts.length}
          </Text>
        </View>
        {contacts.map((e, i) => {
          const { family_name, given_name, postal_addresses = [], voter_reg_matched, voter_reg_contacted, voter_reg_checked } = e
          let location = ''
          if (postal_addresses.length > 0) {
            const { city: contactCity = '', postal_code: postal = '', state: contactState = '', street = '' } = postal_addresses[0]
            if (contactCity) {
              location = contactCity
            }
            if (contactState) {
              if (contactCity) {
                location = `${location}, ${contactState}`
              } else {
                location = contactState
              }
            }
          } else {
            location = '-'
          }

          if (voter_reg_matched || voter_reg_contacted || voter_reg_checked) {
            return (
              <TouchableOpacity disabled={checked} onPress={() => handleOnPress(e)} key={`person-${i}`} style={styles.containerChecked}>
                <View style={styles.person}>
                  <Image transition={false} style={styles.picture} source={require('../../../../assets/images/noUser.png')} />
                  <View>
                    <Text style={styles.name}>
                      {given_name} {family_name}
                    </Text>
                    <Text style={styles.location}>{location}</Text>
                  </View>
                </View>
                <View style={voter_reg_matched || voter_reg_contacted ? styles.checkDone : styles.check}>{getButtonText(e)}</View>
              </TouchableOpacity>
            )
          } else {
            return null
          }
        })}
      </View>
    </View>
  )
}

export default ChooseContact
