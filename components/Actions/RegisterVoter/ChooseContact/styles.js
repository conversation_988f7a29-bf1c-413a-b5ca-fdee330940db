import { StyleSheet } from 'react-native'
import { medium } from '../../../Utils/sizes'
import { unifiedBlue, unifiedDarkGray, unifiedGreen, unifiedLightGray } from '../../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      checkedContacts: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 22,
        marginRight: 22,
        marginBottom: 14,
      },
      checkedContactsText: {
        fontSize: 14,
        marginTop: 22,
        color: monochroma,
        fontWeight: medium,
      },
      buttonHolder: {
        paddingLeft: 20,
        paddingRight: 20,
      },
      inCompleteButton: {
        backgroundColor: unifiedLightGray,
        borderRadius: 20,
        marginTop: 32,
        marginBottom: 24,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
      },
      completeButton: {
        backgroundColor: unifiedGreen,
        borderRadius: 20,
        marginTop: 32,
        marginBottom: 24,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
      },
      completeButtonText: {
        fontWeight: medium,
        color: 'white',
        fontSize: 14,
      },
      body: {
        backgroundColor: 'transparent',
      },
      recomended: {
        fontSize: 14,
        width: '100%',
        marginBottom: 14,
        color: monochroma,
        fontWeight: medium,
        textAlign: 'left',
        marginTop: 22,
        marginLeft: 22,
        marginRight: 22,
      },
      load: {
        fontSize: 14,
        marginTop: 15,
        width: '100%',
        marginBottom: 0,
        color: '#1DA1F2',
        textAlign: 'left',
        marginRight: 22,
        marginLeft: 22,
      },
      container: {
        backgroundColor: isDarkMode ? unifiedDarkGray : 'rgb(244,244,244)',
        borderRadius: 8,
        flexDirection: 'row',
        padding: 17,
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
        marginLeft: 22,
        marginRight: 22,
      },
      containerChecked: {
        backgroundColor: 'rgba(30,215,29,0.1)',
        borderRadius: 8,
        flexDirection: 'row',
        padding: 17,
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
        marginLeft: 22,
        marginRight: 22,
      },
      person: {
        flexDirection: 'row',
        alignItems: 'center',
      },
      picture: {
        width: 42,
        height: 42,
        borderRadius: 999,
        marginRight: 14,
      },
      name: {
        fontSize: 17,
        color: monochroma,
        fontWeight: medium,
        marginBottom: 3,
      },
      location: {
        fontSize: 11,
        color: monochroma,
      },
      check: {
        color: unifiedBlue,
        fontSize: 14,
      },
      checkDone: {
        color: unifiedGreen,
        fontSize: 14,
      },
    })
  }
}
