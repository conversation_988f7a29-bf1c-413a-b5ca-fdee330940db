import { StyleSheet } from 'react-native'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'

    return StyleSheet.create({
      return: {
        fontSize: 14,
        color: '#1DA1F2',
        width: '100%',
        textAlign: 'center',
      },
      container: {
        marginTop: 38,
        paddingRight: 20,
        paddingLeft: 20,
      },
      title: {
        fontSize: 14,
        fontWeight: medium,
        color: monochroma,
      },
      text: {
        fontSize: 14,
        color: monochroma,
      },
      actionsContainer: {
        marginTop: 20,
        marginBottom: 30,
      },
      buttons: {
        marginTop: 24,
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
      button: {
        alignItems: 'center',
        justifyContent: 'center',
      },
      buttonText: {
        maxWidth: 110,
        fontSize: 14,
        marginTop: 6,
        textAlign: 'center',
        color: monochroma,
      },
    })
  }
}
