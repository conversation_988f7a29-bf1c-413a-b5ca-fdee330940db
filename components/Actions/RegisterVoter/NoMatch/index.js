/* eslint-disable react-native/no-inline-styles */
import Pill from '../../../Pill'
import SendSMS from 'react-native-sms'
import StyleSheetFactory from './styles'
import React, { useContext, useEffect } from 'react'
import { sendEmail } from '../../../Utils/sendEmail'
import { useVoterRegActionState } from '../LocalStore'
import { useGlobalState } from '../../../../contexts/store'
import { faCommentDots } from '@fortawesome/pro-solid-svg-icons'
import { faEnvelope, faUndo } from '@fortawesome/pro-regular-svg-icons'
import { View, Text, useColorScheme, TouchableOpacity, Alert, Platform, Linking } from 'react-native'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const NoMatch = ({ isVisible = false, setStep, item, activePostId = '', setVisible, contact = {} }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  const { given_name = '', phone_numbers = [], email_addresses = [] } = contact

  //Global
  const [globalState] = useGlobalState()

  //Local state
  const [localState] = useVoterRegActionState()

  useEffect(() => {
    console.log('ALTERED RESPONSE', localState.response)
  }, [localState.response])

  const retry = () => setStep(0)
  const { setToasts, toastRef } = useContext(ToastContext)

  const setFlowComplete = async voter => {
    try {
      const result = globalState.api.flows.updateFlow(localState.flow.id, localState.flow.state, 'complete', { contacted: true })
      console.log('END!', result)
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'Thank you! Feel free to contact some other potential voters!', type: 'notice' }])
      toastRef.current.show()
      setStep(0)
    } catch (e) {
      let error = 'There was a communication error. Please try again.'
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e?.info?.responseBody?.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else {
          error = e?.info?.responseBody?.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
    }
  }

  const sendTextAndroid = (body, recipient) => {
    const url = `sms:${recipient}?body=${body}`
    Linking.canOpenURL(url)
      .then(supported => {
        if (!supported) {
          console.log('WRONG TEXT ANDROID: ' + url)
          setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'There was an error. Please try again.', type: 'error' }])
          toastRef.current.show()
        } else {
          setTimeout(setFlowComplete, 500)
          return Linking.openURL(url)
        }
      })
      .catch(err => {
        console.error('An error occurred', err)
        setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'There was an error. Please try again.', type: 'error' }])
        toastRef.current.show()
      })
  }

  const sendText = () => {
    const body = localState.response.no_voter_matches.templates.sms
    let phone = ''
    if (phone_numbers.length === 0) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'This contact has no mobile phone number associated to it.', type: 'error' }])
      toastRef.current.show()
      return
    } else {
      phone = phone_numbers[0].number
    }
    if (Platform.OS === 'android') {
      sendTextAndroid(body, phone)
    } else {
      SendSMS.send(
        {
          body,
          recipients: [phone],
          successTypes: ['sent', 'queued'],
          allowAndroidSendWithoutReadPermission: true,
        },
        async (completed, cancelled, error) => {
          console.log('SMS Callback: completed: ' + completed + ' cancelled: ' + cancelled + 'error: ' + error)
          if ((Platform.OS === 'ios' && completed) || Platform.OS === 'android') {
            try {
              setFlowComplete()
            } catch (e) {
              console.log('ERROR', e)
              setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'There was an error. Please try again.', type: 'error' }])
              toastRef.current.show()
            }
          }
        },
      )
    }
  }

  const sendEmailAux = async () => {
    const body = localState.response.no_voter_matches.templates.email
    let email = ''
    if (email_addresses.length === 0 || email_addresses[0].email.trim() === '') {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'This contact has no email address associated to it.', type: 'error' }])
      toastRef.current.show()
      return
    } else {
      email = email_addresses[0].email
    }
    sendEmail(email, 'Voter Registration', body)
    setFlowComplete()
  }

  return (
    <View style={isVisible ? styles.container : { display: 'none' }}>
      <View style={{ backgroundColor: '#FFE8F3', paddingVertical: 11, paddingHorizontal: 17, borderRadius: 10 }}>
        <Text style={styles.title}>Uh oh!</Text>
        <Text style={{ ...styles.text, marginBottom: 30 }}>We couldn't match {given_name} to any to any registered voters.</Text>
        <Text style={styles.title}>Why did this happen?</Text>
        <Text style={styles.text}>
          <Text style={{ fontSize: 16 }}>•</Text> They're not registered to vote.
        </Text>
        <Text style={styles.text}>
          <Text style={{ fontSize: 16 }}>•</Text> The name doesn't match what's on file.
        </Text>
        <Text style={styles.text}>
          <Text style={{ fontSize: 16 }}>•</Text> The address doesn't match what's on file.
        </Text>
      </View>
      <View style={styles.actionsContainer}>
        <Text style={styles.title}>What you can do:</Text>
        <View style={{ marginBottom: 8, marginTop: 14 }}>
          <Pill icon={faCommentDots} text="Text them info on how to register" onPress={() => sendText()} />
        </View>
        <View style={{ marginBottom: 8 }}>
          <Pill icon={faEnvelope} text="Email them info on how to register" onPress={() => sendEmailAux()} />
        </View>
        <View style={{ marginBottom: 8 }}>
          <Pill icon={faUndo} text="Try searching for them again" onPress={retry} />
        </View>
      </View>
      <TouchableOpacity onPress={() => setStep(0)}>
        <Text style={styles.return}>Return to contacts</Text>
      </TouchableOpacity>
    </View>
  )
}

export default NoMatch
