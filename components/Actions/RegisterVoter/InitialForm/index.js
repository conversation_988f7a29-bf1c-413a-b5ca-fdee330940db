/* eslint-disable react-native/no-inline-styles */
import React, { useContext, useEffect, useState } from 'react'
import StyleSheetFactory from './styles'
import UText from '../../../Inputs/UText'
import UCombo from '../../../Inputs/UCombo'
import { USA_STATES } from '../../../Utils/states'
import UDatePicker from '../../../Inputs/UDatePicker'
import { useVoterRegActionState } from '../LocalStore'
import { unifiedDarkGray, unifiedLightGray } from '../../../Utils/colors'
import { useGlobalState } from '../../../../contexts/store'
import { View, Text, useColorScheme, TouchableOpacity, Alert, Modal, ActivityIndicator } from 'react-native'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const InitialForm = ({ isVisible = false, item, setStep, activePostId = '', contact = {} }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  //States
  const [city, setCity] = useState('')
  const [date, setDate] = useState('')
  const [fname, setFname] = useState('')
  const [lname, setLname] = useState('')
  const [address, setAddress] = useState('')
  const [zipCode, setZipCode] = useState('')
  const [stateUS, setStateUS] = useState('')
  const [loading, setLoading] = useState(false)

  //Global
  const [globalState, globalActions] = useGlobalState()
  //Local
  const [localState, localActions] = useVoterRegActionState()

  useEffect(() => {
    console.log('The date has changed', date)
  }, [date])

  useEffect(() => {
    const { date_of_birth = '', postal_addresses = [], family_name = '', given_name = '', middle_name = '' } = contact
    setFname(`${given_name} ${middle_name}`.trim())
    setLname(family_name)
    if (postal_addresses.length > 0) {
      const { city: contactCity = '', postal_code: postal = '', state: contactState = '', street = '' } = postal_addresses[0]
      if (contactCity) {
        setCity(contactCity)
      }
      if (postal) {
        setZipCode(postal)
      }
      if (contactState) {
        setStateUS(contactState)
      }
      if (street) {
        setAddress(street)
      }
    }
    if (date_of_birth) {
      const birthday = date_of_birth.split('-')
      setDate({
        day: parseInt(birthday[2], 10),
        month: parseInt(birthday[1], 10),
        year: parseInt(birthday[0], 10),
      })
    }
  }, [contact])

  const handleStateChange = (e, i) => {
    setStateUS(e)
  }

  const deleteEmptyInfo = obj => {
    const clone = JSON.parse(JSON.stringify(obj))
    console.log('CLONE', clone)
    Object.keys(clone).forEach(e => {
      if (clone[e].trim() === '') {
        delete clone[e]
      }
    })
    if (Object.keys(clone).length === 0) {
      return null
    }
    console.log('RETURNED CLONE', clone)
    return clone
  }

  const { setToasts, toastRef } = useContext(ToastContext)

  const checkRegistration = async () => {
    setTimeout(() => {
      setLoading(true)
    }, 100)
    try {
      const { year = 0, month = 0, day = 0 } = date
      const possibleDate = { year, month, day }

      const formContact = {
        name: deleteEmptyInfo({ given: fname, family: lname }),
        address: deleteEmptyInfo({ first_line: address, city, state: stateUS, postal_code: zipCode }),
      }

      if (date) {
        if (!year && (month || day)) {
          setLoading(false)
          setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'Year is required.', type: 'error' }])
          toastRef.current.show()
          return
        } else {
          if (!Number.isInteger(possibleDate.year)) {
            possibleDate.year = 1
          }
          if (!Number.isInteger(possibleDate.month)) {
            possibleDate.month = 1
          }
          if (!Number.isInteger(possibleDate.day)) {
            possibleDate.day = 1
          }
          formContact.birthday = possibleDate
        }
      }

      Object.keys(formContact).forEach(e => {
        if (formContact[e] === null) {
          delete formContact[e]
        }
      })

      if (formContact.birthday.year === 1) {
        delete formContact.birthday
      }

      const data = {
        _updated_contact: formContact,
      }

      console.log('UPDATED CONTACT', formContact)

      const result = await globalState.api.flows.updateFlow(localState.flow.id, localState.flow.state, 'forward', data)
      localActions.setFlow(result)
      console.log('RESULT FORM', JSON.stringify(result))

      const { data: rawData } = result
      await localActions.setResponse(rawData)
      setLoading(false)

      setTimeout(() => {
        const { state: flowState } = result
        if (flowState === 'no_voter_matches') {
          setStep(2)
        } else {
          localActions.setMatchedData({ contactId: contact.id, activePostId, actionId: item.id, fname, lname, address, city, stateUS, zipCode, possibleDate })
          setStep(3)
        }
      }, 100)
    } catch (e) {
      console.log('ERROR ##', e)
      let error = 'There was a communication error. Please try again.'
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e?.info?.responseBody?.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else {
          error = e?.info?.responseBody?.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
      setTimeout(() => {
        setLoading(false)
      }, 100)
    }
  }

  return (
    <View style={isVisible ? styles.inputs : { display: 'none' }}>
      <Modal animationType="none" transparent visible={loading}>
        <View style={{ flex: 1, backgroundColor: unifiedDarkGray, opacity: 0.8, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size={'large'} color="black" />
        </View>
      </Modal>
      <Text style={styles.advice}>Enter as much information you know about this contact. Then click "Check Registration" below.</Text>
      <Text style={styles.label}>First name</Text>
      <UText value={fname} onChangeText={setFname} />
      <Text style={styles.label}>Last name</Text>
      <UText value={lname} onChangeText={setLname} customStyles={{ marginTop: 7 }} />
      <Text style={styles.label}>Street address</Text>
      <UText value={address} onChangeText={setAddress} customStyles={{ marginTop: 7 }} />
      <Text style={styles.label}>City</Text>
      <UText value={city} onChangeText={setCity} customStyles={{ marginTop: 7 }} />
      <Text style={styles.label}>State</Text>
      <UCombo value={stateUS} onValueChange={handleStateChange} customStyles={{ marginTop: 8 }} dataSet={USA_STATES} defaultText="--" />
      <Text style={styles.label}>Zip code</Text>
      <UText value={zipCode} onChangeText={setZipCode} customStyles={{ marginTop: 7 }} keyboardType="number-pad" />
      <Text style={styles.label}>Date of birth</Text>
      <UDatePicker value={date} onValueChange={setDate} customStyles={{ marginTop: 7 }} />
      <TouchableOpacity onPress={checkRegistration} style={styles.sendButton}>
        <Text style={styles.sendButtonText}>Check Registration</Text>
      </TouchableOpacity>
    </View>
  )
}

export default InitialForm
