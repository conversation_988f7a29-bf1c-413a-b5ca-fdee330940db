import { StyleSheet } from 'react-native'
import { unifiedBlue, unifiedDarkGray } from '../../../Utils/colors'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      label: {
        marginTop: 21,
        fontSize: 14,
        color: monochroma,
      },
      advice: {
        fontSize: 14,
        color: unifiedDarkGray,
        marginBottom: 27,
      },
      return: {
        fontSize: 14,
        color: '#1DA1F2',
        width: '100%',
        textAlign: 'center',
      },
      inputs: {
        backgroundColor: 'transparent',
        marginTop: 17,
        paddingLeft: 22,
        paddingRight: 22,
      },
      sendButton: {
        backgroundColor: unifiedBlue,
        borderRadius: 8,
        marginTop: 34,
        height: 54,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 58,
      },
      sendButtonText: {
        fontWeight: medium,
        color: 'white',
        fontSize: 14,
      },
    })
  }
}
