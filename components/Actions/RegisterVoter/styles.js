import { StyleSheet } from 'react-native'
import { unifiedRoyalBlue } from '../../Utils/colors'
import { medium } from '../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

    return StyleSheet.create({
      noContactsToDo: {
        margin: 22,
        fontSize: 14,
        color: monochroma,
        fontWeight: medium,
      },
      noContactsBullet: {
        fontSize: 14,
        color: monochroma,
        fontWeight: medium,
      },
      noContactsText: {
        fontSize: 14,
        color: 'black',
      },
      noContactsTitle: {
        fontSize: 14,
        color: 'black',
        fontWeight: medium,
        marginBottom: 25,
      },
      pinkHolder: {
        backgroundColor: '#FFE8F3',
        marginTop: 14,
        marginLeft: 22,
        marginRight: 22,
        padding: 17,
        borderRadius: 10,
      },
      noContacts: {
        width: '100%',
        marginTop: 14,
        lineHeight: 20,
        fontSize: 14,
        textAlign: 'center',
        fontWeight: medium,
        color: monochroma,
        paddingLeft: 36,
        paddingRight: 36,
      },
      voterRegistration: {
        paddingBottom: 40,
        backgroundColor: monochroma2,
      },
    })
  }
}
