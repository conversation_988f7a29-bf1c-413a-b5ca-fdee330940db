/* eslint-disable quotes */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import Header from './Header'
import NoMatch from './NoMatch'
import Matched from './Matched'
import InitialForm from './InitialForm'
import StyleSheetFactory from './styles'
import MatchesFound from './MatchesFound'
import ChooseContact from './ChooseContact'
import React, { useContext, useEffect, useRef, useState } from 'react'
import FormWrapper from '../../Wrappers/FormWrapper'
import { useVoterRegActionState } from './LocalStore'
import { useGlobalState } from '../../../contexts/store'
import { Text, useColorScheme, Alert, View, ActivityIndicator } from 'react-native'
import Pill from '../../Pill'
import { faAddressBook } from '@fortawesome/pro-solid-svg-icons'
import { Navigation } from 'react-native-navigation'
import GenericOptions from '../../GenericOptions'
import { ToastContext } from '../../Home/ToastStackWrapper'
import { useMMKVBoolean } from 'react-native-mmkv'

const RegisterVoter = ({ item = {}, activePostId = '', setVisible = () => '', componentId, refreshParent = () => '' }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const monochroma = isDarkMode ? 'white' : 'black'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  //Global State
  const [globalState] = useGlobalState()

  //MMKV
  const [confirmed = false] = useMMKVBoolean('IS_CONFIRMED', globalState.mmkv)

  //Local State
  const [localState, localActions] = useVoterRegActionState()

  //Inner States
  const [step, setStep] = useState(0)
  const [contacts, setContacts] = useState([])
  const [reported, setReported] = useState(false)
  const [loading, setLoading] = useState(false)
  const [notAvailable, setNotAvailable] = useState(false)

  // Refs
  const optionsRef = useRef(null)
  const reportDialogRef = useRef(null)

  const { contact } = localState
  //Context
  const { setToasts, toastRef } = useContext(ToastContext)
  const { showConfirmActionSheet } = useContext(ToastContext)

  const getActionInstance = async () => {
    console.log('ACTION INSTANCE', item)
    setLoading(true)
    try {
      const result = await globalState.api.actionInstance.createActionInstance(item.id)
      console.log('RESULT AI', JSON.stringify(result, null, 3))
      await startFlow(result.id)
    } catch (e) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'There was a communication error.', type: 'error' }])
      toastRef.current.show()
      console.log('ERROR', JSON.stringify(e))
    }

    setLoading(false)
  }

  const startFlow = async actionInstance => {
    try {
      localActions.setProgress(0.4)
      const result = await globalState.api.flows.createFlow('voter-reg', actionInstance)
      console.log('RESULT Flow', result)
      localActions.setFlow(result)
      getContacts(result.id, result.state)
    } catch (e) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'There was a communication error.', type: 'error' }])
      toastRef.current.show()
      console.log('ERROR', JSON.stringify(e))
    }
  }

  const getContacts = async (flowId, flowState) => {
    try {
      const result = await globalState.api.flows.updateFlow(flowId, flowState, 'forward')
      console.log('CONTACTS', JSON.stringify(result))
      localActions.setFlow(result)
      const { recommend_contacts: recommendContacts } = result.data
      // const { targets } = result
      setContacts(recommendContacts.initial_contacts)
      if (recommendContacts.initial_contacts.length === 0) {
        setNotAvailable(true)
      } else {
        setNotAvailable(false)
      }
    } catch (e) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'There was a communication error.', type: 'error' }])
      toastRef.current.show()
      console.log('ERROR CONTACTS', JSON.stringify(e))
      setNotAvailable(true)
    }
  }

  const gotoSyncContacts = () => {
    if (!confirmed) {
      showConfirmActionSheet('before syncing contacts.')
      return
    }

    setTimeout(() => {
      Navigation.push(componentId, {
        component: {
          name: 'SyncContacts',
          passProps: {
            updateTotal: () => {},
            setReloadKey: () => setTimeout(localActions.setReloadKey, 2000),
          },
          options: {
            bottomTabs: {
              visible: false,
              drawBehind: true,
            },
            topBar: {
              noBorder: true,
              scrollEdgeAppearance: {
                noBorder: true,
              },
              elevation: 0,
              visible: true,
              backButton: {
                color: { light: 'black', dark: 'white' },
              },
              title: {
                component: {
                  name: 'SyncContactsHeader',
                },
              },
            },
          },
        },
      })
    }, 350)
  }

  useEffect(() => {
    if (step === 0) {
      localActions.reset()
      getActionInstance()
    }
  }, [step])

  useEffect(() => {
    if (step === 0) {
      console.log('HERE------------')
      getActionInstance()
    }
  }, [localState.reloadKey])

  useEffect(() => {
    return () => {
      console.log('UNMOUNT REGISTER VOTER')
      refreshParent()
    }
  }, [])

  const Bullet = ({ text }) => {
    return (
      <View style={{ flexDirection: 'row' }}>
        <Text style={styles.noContactsBullet}>•</Text>
        <Text style={styles.noContactsText}>{text}</Text>
      </View>
    )
  }

  return (
    <FormWrapper style={styles.voterRegistration}>
      {!reported && (
        <>
          <Header optionsRef={optionsRef} contact={contact} step={step} item={item} componentId={componentId} />
          {/* {notAvailable && <Text style={styles.noContacts}>Unfortunately you don't have any matching contacts for this action</Text>} */}
          {notAvailable && (
            <>
              <View style={styles.pinkHolder}>
                <Text style={styles.noContactsTitle}>You don't have any matching contacts!</Text>
                <Text style={[styles.noContactsText, { marginBottom: 5 }]}>This could have happened because:</Text>
                <Bullet text={"You haven't synced your contacts"} />
                <Bullet text={"You've synced your contacts but don't have any that match the filter for this action"} />
              </View>
              <Text style={styles.noContactsToDo}>What you can do:</Text>
              <View style={[{ marginLeft: 22, marginRight: 22 }]}>
                <Pill icon={faAddressBook} text={'Sync (or resync) your contacts'} onPress={gotoSyncContacts} />
              </View>
            </>
          )}
          {loading && (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingTop: 15 }}>
              <ActivityIndicator size={'large'} />
            </View>
          )}
          {contacts.length > 0 && (
            <>
              <ChooseContact setVisible={setVisible} activePostId={activePostId} item={item} getContacts={getContacts} setStep={setStep} isVisible={step === 0} contacts={contacts} />
              {/* <ChooseContact checked setVisible={setVisible} activePostId={activePostId} item={item} getContacts={getContacts} setStep={setStep} isVisible={step === 0} contacts={contacts} /> */}
              {step === 1 && <InitialForm contact={contact} activePostId={activePostId} item={item} isVisible={step === 1} setStep={setStep} />}
              <NoMatch contact={contact} setVisible={setVisible} item={item} isVisible={step === 2} setStep={setStep} activePostId={activePostId} />
              <MatchesFound item={item} isVisible={step === 3} setStep={setStep} />
              <Matched setVisible={setVisible} isVisible={step === 4} setStep={setStep} />
            </>
          )}
        </>
      )}
      {reported && (
        <View style={{ marginLeft: 22, marginRight: 22, flexDirection: 'row', backgroundColor: 'transparent', justifyContent: 'space-between' }}>
          <View style={{ padding: 14, borderColor: '#F72585', borderWidth: 1, borderRadius: 8 }}>
            <Text style={{ fontSize: 14, color: monochroma }}>You reported this action previously, so it is no longer visible on your feed.</Text>
          </View>
        </View>
      )}
      <GenericOptions
        cb={() => {
          setReported(true)
          refreshParent()
          item.is_reported = true // hacky way so it looks faster, will be overwritten by the correct info in about 3s
        }}
        type="action"
        optionstRef={optionsRef}
        reportDialogRef={reportDialogRef}
        oid={item.id}
      />
    </FormWrapper>
  )
}

export default RegisterVoter
