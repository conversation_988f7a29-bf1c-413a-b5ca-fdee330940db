/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import StyleSheetFactory from './styles'
import React, { useContext, useEffect, useState } from 'react'
import { useVoterRegActionState } from '../LocalStore'
import { useGlobalState } from '../../../../contexts/store'
import { useHomeFeedState } from '../../../Home/LocalStore'
import { faAngleRight } from '@fortawesome/pro-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { View, Text, useColorScheme, TouchableOpacity, Alert } from 'react-native'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const MatchesFound = ({ isVisible = false, setStep, item }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  //GlobalState
  const [globalState] = useGlobalState()

  //Local state
  const [localState, localActions] = useVoterRegActionState()
  const [voters, setVoters] = useState([])
  const [isMatching, setMatching] = useState(false)

  //Homefeed state
  const [homeState, homeActions] = useHomeFeedState()

  const retry = async () => {
    const result = await globalState.api.flows.updateFlow(localState.flow.id, localState.flow.state, 'fail')
    console.log('RETRY!', result)
    localActions.setResponse(result.data)
    setStep(2)
  }

  useEffect(() => {
    if (localState.flow?.data?.voter_lookup?.matches) {
      setVoters(localState.flow.data.voter_lookup.matches)
      localActions.setProgress(0.6)
    }
  }, [localState.flow])

  const { setToasts, toastRef } = useContext(ToastContext)

  const matchRegistration = async voter => {
    if (isMatching) {
      return
    }
    setMatching(true)
    const { id: voterId } = voter
    try {
      const result = await globalState.api.flows.updateFlow(localState.flow.id, localState.flow.state, 'success', { chosen_voter_id: voterId })
      console.log('END!', result)
      setStep(4)
    } catch (e) {
      console.log('ERROR VOTER REG CHOOSING', JSON.stringify(e))
      let error = 'There was a communication error. Please try again.'
      // if (e?.info?.responseBody?.detail) {
      //   error = e.info.responseBody.detail.join('/n')
      // }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: error, type: 'error' }])
      toastRef.current.show()
    }
    setMatching(false)
  }

  return (
    <View style={isVisible ? styles.container : { display: 'none' }}>
      <View>
        <Text style={styles.title}>We found multiple matches!</Text>
        <Text style={{ ...styles.text, marginBottom: 8 }}>Are any of these your contact?</Text>
        {voters.map((e, i) => {
          return (
            <TouchableOpacity onPress={() => matchRegistration(e)} style={styles.contact} key={`${i}`}>
              <View style={styles.leftContainer}>
                <Text style={styles.name}>
                  {e.given_name} {e.family_name}
                </Text>
                <Text style={styles.location}>
                  {e.age_range} year old in {e.location}
                </Text>
              </View>
              <Text style={styles.select}>
                <FontAwesomeIcon size={22} icon={faAngleRight} color={'black'} />
              </Text>
            </TouchableOpacity>
          )
        })}
        <TouchableOpacity onPress={retry} style={styles.contact}>
          <View style={styles.leftContainer}>
            <Text style={styles.name}>None of these matches are correct</Text>
            <Text style={styles.location}>See what you can do</Text>
          </View>
          <Text style={styles.select}>
            <FontAwesomeIcon size={22} icon={faAngleRight} color={'black'} />
          </Text>
        </TouchableOpacity>
      </View>
      <TouchableOpacity onPress={() => setStep(0)}>
        <Text style={styles.return}>Return to contacts</Text>
      </TouchableOpacity>
    </View>
  )
}

export default MatchesFound
