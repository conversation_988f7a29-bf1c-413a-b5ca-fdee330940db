import { StyleSheet } from 'react-native'
import { medium } from '../../../Utils/sizes'
import { unifiedBlue, unifiedDarkGray } from '../../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'

    return StyleSheet.create({
      contact: {
        backgroundColor: isDarkMode ? unifiedDarkGray : 'rgb(244,244,244)',
        borderRadius: 8,
        flexDirection: 'row',
        padding: 17,
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
      },
      leftContainer: {},
      name: {
        fontSize: 17,
        color: monochroma,
        fontWeight: medium,
      },
      location: {
        color: monochroma,
        fontSize: 11,
      },
      select: {
        color: unifiedBlue,
        fontSize: 14,
      },
      container: {
        marginTop: 38,
        paddingRight: 22,
        paddingLeft: 22,
      },
      title: {
        fontSize: 14,
        fontWeight: medium,
        color: monochroma,
      },
      text: {
        fontSize: 14,
        color: monochroma,
      },
      return: {
        fontSize: 14,
        color: '#1DA1F2',
        width: '100%',
        textAlign: 'center',
        marginBottom: 30,
      },
    })
  }
}
