/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
import React, { useEffect } from 'react'
import StyleSheetFactory from './styles'
import { View, Text, useColorScheme, TouchableOpacity } from 'react-native'
import { unifiedGreen } from '../../../Utils/colors'
import { faRegistered } from '@fortawesome/pro-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { useVoterRegActionState } from '../LocalStore'

const Matched = ({ isVisible = false, setVisible, setStep }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)

  const [localState, localActions] = useVoterRegActionState()

  useEffect(() => {
    if (isVisible) {
      localActions.setProgress(1)
    }
  }, [isVisible])

  return (
    <View style={isVisible ? styles.container : { display: 'none' }}>
      <View style={styles.goodNewsContainer}>
        <View style={styles.goodNewsLeft}>
          <Text style={styles.goodNews}>Good news!</Text>
          <Text style={styles.goodNewsText}>This person is registered to vote!</Text>
        </View>
        {/* <FontAwesomeIcon color={unifiedGreen} size={30} icon={faRegistered} /> */}
      </View>
      {/* <View style={styles.votingContainer}>
        <Text style={styles.voting}>Voting History</Text>
        <View style={styles.history}>
          <View style={styles.election}>
            <Text style={styles.period}>2020 General</Text>
            <FontAwesomeIcon color={unifiedGreen} size={16} icon={faCheck} />
          </View>
          <View style={styles.election}>
            <Text style={styles.period}>2020 General</Text>
            <FontAwesomeIcon color={unifiedPinkRed} size={16} icon={faTimes} />
          </View>
        </View>
      </View> */}
      <TouchableOpacity onPress={() => setStep(0)} style={styles.completeButton}>
        <Text style={styles.sendButtonText}>Voter matched!</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={() => setStep(0)} style={styles.close}>
        <Text style={styles.closeText}>Return to contacts</Text>
      </TouchableOpacity>
    </View>
  )
}

export default Matched
