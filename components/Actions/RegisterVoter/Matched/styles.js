import { StyleSheet } from 'react-native'
import { unifiedGreen } from '../../../Utils/colors'
import { medium } from '../../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      container: {
        paddingTop: 36,
        paddingLeft: 20,
        paddingRight: 20,
      },
      goodNewsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 30,
      },
      goodNews: {
        fontSize: 14,
        color: monochroma,
        fontWeight: medium,
        marginBottom: 2,
      },
      goodNewsText: {
        fontSize: 14,
        color: monochroma,
      },
      voting: {
        fontSize: 16,
        fontWeight: medium,
        color: monochroma,
        marginBottom: 8,
      },
      election: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
      },
      period: {
        fontSize: 14,
        color: monochroma,
      },
      completeButton: {
        backgroundColor: unifiedGreen,
        borderRadius: 20,
        marginTop: 24,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
      },
      sendButtonText: {
        fontWeight: medium,
        color: 'white',
        fontSize: 14,
      },
      close: {
        marginTop: 14,
      },
      closeText: {
        width: '100%',
        textAlign: 'center',
        color: '#1DA1F2',
      },
    })
  }
}
