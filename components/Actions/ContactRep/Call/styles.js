import { StyleSheet } from 'react-native'
import { medium } from '../../../Utils/sizes'
import { darkModeGray, unifiedBlue, unifiedGreen } from '../../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      phoneContainer: {
        backgroundColor: 'transparent',
        padding: 20,
        paddingTop: 0,
        marginTop: 28,
      },
      call: {
        fontWeight: medium,
        color: monochroma,
        fontSize: 14,
      },
      number: {
        fontWeight: medium,
        color: monochroma,
        fontSize: 20,
        marginTop: 6,
      },
      say: {
        fontWeight: medium,
        color: monochroma,
        fontSize: 14,
        marginTop: 18,
      },
      text: {
        color: monochroma,
        marginTop: 8,
        fontSize: 14,
      },
      sendButton: {
        backgroundColor: unifiedBlue,
        borderRadius: 8,
        marginTop: 24,
        height: 54,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
      },
      sentButton: {
        backgroundColor: unifiedGreen,
        borderRadius: 8,
        marginTop: 24,
        height: 54,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
      },
      sendButtonText: {
        fontWeight: medium,
        color: 'white',
        fontSize: 14,
      },
    })
  }
}
