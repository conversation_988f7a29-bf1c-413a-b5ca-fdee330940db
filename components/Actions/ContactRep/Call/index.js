/* eslint-disable react-native/no-inline-styles */
import React from 'react'
import StyleSheetFactory from './styles'
import { Text, Linking, TouchableOpacity, useColorScheme, View, Alert } from 'react-native'
import { medium } from '../../../Utils/sizes'
import Pill from '../../../Pill'
import { faPhone } from '@fortawesome/pro-solid-svg-icons'

const Call = ({ completeAction, called, phoneNumber = '', say, setCalled, emailText }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  return (
    <>
      {phoneNumber.trim() === '' ? (
        <>
          <View style={{ backgroundColor: '#FFE8F3', marginLeft: 22, marginRight: 22, marginTop: 22, padding: 13, borderRadius: 8 }}>
            <Text style={{ color: 'black', fontSize: 14, fontWeight: medium }}>Unified does not have a phone number for this rep</Text>
            <Text style={{ color: 'black', fontSize: 14 }}>This means you won't be able to talk them right now.</Text>
          </View>
          <View style={{ marginLeft: 22, marginRight: 22, marginTop: 22 }}>
            <Text style={{ color: monochroma, fontSize: 14, fontWeight: medium, marginBottom: 7 }}>What you can do:</Text>
            <Pill onPress={() => Linking.openURL('mailto:<EMAIL>')} icon={faPhone} text="Help us update our information" />
          </View>
        </>
      ) : (
        <View style={styles.phoneContainer}>
          <Text style={[styles.call, { marginBottom: 8 }]}>Call script:</Text>
          <View style={{ minHeight: 200, borderWidth: 1, borderColor: '#BABABA', borderRadius: 3, paddingLeft: 11, paddingRight: 11 }}>
            <Text style={styles.text}>{say}</Text>
          </View>
        </View>
      )}
    </>
  )
}

export default Call
