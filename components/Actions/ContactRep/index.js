/* eslint-disable quotes */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import Call from './Call'
import Email from './Email'
import Pill from '../../Pill'
import StyleSheetFactory from './styles'
import { medium } from '../../Utils/sizes'
import SocialBlurp from '../../SocialBlurp'
import GenericOptions from '../../GenericOptions'
import { getFormat } from '../../Utils/dateFormat'
import { Navigation } from 'react-native-navigation'
import FormWrapper from '../../Wrappers/FormWrapper'
import { useGlobalState } from '../../../contexts/store'
import { useHomeFeedState } from '../../Home/LocalStore'
import { SceneMap, TabView } from 'react-native-tab-view'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { useCreatePostState } from '../../CreatePost/LocalStore'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { executeNavigation, executeNavigationPostCreation } from './navigation'
import { faEllipsisV, faEnvelope, faPhone } from '@fortawesome/pro-regular-svg-icons'
import { unifiedBlue, unifiedDarkGray, unifiedLightGray, unifiedRed, unifiedRoyalBlue } from '../../Utils/colors'
import { faRepublican, faDemocrat, faEnvelope as envelopeSolid, faPhone as phoneSolid, faUser, faClipboard } from '@fortawesome/pro-solid-svg-icons'
import { View, Text, Image, useColorScheme, TouchableOpacity, ActivityIndicator, Pressable, Animated, useWindowDimensions, Alert, Linking, Platform } from 'react-native'
import SimpleTabs from './SimpleTabs'
import { sendEmail } from '../../Utils/sendEmail'
import { ToastContext } from '../../Home/ToastStackWrapper'
import { useMMKVBoolean } from 'react-native-mmkv'

const ContactRep = ({ setVisible, activePostId, item, componentId, refreshParent = () => '' }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'
  const monochroma2 = !isDarkMode ? 'white' : unifiedRoyalBlue

  // Global State
  const [globalState] = useGlobalState()

  //MMKV
  const [confirmed = false] = useMMKVBoolean('IS_CONFIRMED', globalState.mmkv)

  //States
  const [action, setAction] = useState(null)
  const [index, setIndex] = React.useState(0)
  const [loading, setLoading] = useState(true)
  const [ownPost, setOwnPost] = useState(false)
  const [completors, setCompletors] = useState([])
  const [externalTab, setExternalTab] = useState(0)
  const [homeState, homeActions] = useHomeFeedState()
  const [isNavigating, setNavigating] = useState(false)
  const [postState, postActions] = useCreatePostState()
  const [isEmailSelected, setEmailSelected] = useState(true)

  // Refs
  const optionsRef = useRef(null)
  const reportDialogRef = useRef(null)

  //Official
  const [name, setName] = useState('')
  const [title, setTitle] = useState('')
  const [photo, setPhoto] = useState('')
  const [party, setParty] = useState('')
  const [share, setShare] = useState(false)
  const [noOfficial, setNoOfficial] = useState(false)

  //Email Form
  const [email, setEmail] = useState('')
  const [subject, setSubject] = useState('')
  const [emailText, setEmailText] = useState('')
  const [emailSent, setEmailSent] = useState(false)
  const emailFormProps = { email, subject, setSubject, emailText, setEmailText, emailSent, setEmailSent }

  //Call View
  const [say, setSay] = useState('')
  const [called, setCalled] = useState(false)
  const [phoneNumber, setPhone] = useState('')
  const callProps = { phoneNumber, say, called, setCalled }

  const { creator } = item
  const { display_name: displayName, profile_pic: picture } = creator

  //Context
  const { showConfirmActionSheet } = useContext(ToastContext)
  const { setToasts, toastRef } = useContext(ToastContext)

  // Use Effects

  useEffect(() => {
    console.log('ITEM CONTACT REP', item.id)
    getCompletors()
    getAction()
  }, [])

  useEffect(() => {
    const testOwnPost = async () => {
      let savedProfile = globalState.mmkv.getString('PROFILE')
      savedProfile = JSON.parse(savedProfile)
      if (item.creator.account_id === savedProfile.account.id) {
        setOwnPost(true)
      }
    }
    console.log('ACITON POST', JSON.stringify(item))
    testOwnPost()
  }, [])

  useEffect(() => {
    if (action) {
      setSay(action?.email_body)
      setEmailText(action?.email_body)
      setSubject(action?.email_subject)
      setName(action.targets.officials[0]?.name)
      setTitle(action.targets.officials[0]?.title)
      setParty(action.targets.officials[0]?.party)
      setPhoto(action.targets.officials[0]?.photo_url)
      action.targets.officials[0]?.phone_numbers?.forEach(e => {
        if (e?.number) {
          setPhone(e.number)
        }
      })
      action.targets.officials[0]?.emails?.forEach(e => {
        if (e?.email) {
          setEmail(e.email)
        }
      })
    }
  }, [action])

  useEffect(() => {
    if (photo) {
      setPhoto(photo.replace('http:', 'https:'))
    }
  }, [photo])

  useEffect(() => {
    return () => {
      console.log('UNMOUNT REGISTER VOTER')
      refreshParent()
    }
  }, [])

  // End useEffects

  const getProfile = async () => {
    try {
      const response = await globalState.api.users.getProfile()
      return response
    } catch (e) {
      console.log('PROFILE ERROR', e)
      return null
    }
  }

  const getCompletors = async () => {
    try {
      const response = await globalState.api.actions.getActionsCompletors(item.id)
      // console.log('COMPLETORS LL', response.length)
      setCompletors(response)
    } catch (e) {
      console.log('PROFILE ERROR LL', JSON.stringify(e))
      setCompletors([])
    }
  }

  const getAction = async () => {
    try {
      const result = await globalState.api.actions.getActionTargets(item.id)
      console.log('DATA I', JSON.stringify(result))
      setAction(result)
      if (result.targets.officials.length === 0) {
        const profile = await getProfile()
        if (profile?.profile?.addresses?.length > 0) {
          await profile.profile.addresses.forEach(async e => {
            if (e.label === 'voter-registration' || e.label === 'main') {
              if (e.postal_code) {
                await setShare(true)
              }
            }
          })
        }
        await setNoOfficial(true)
      } else {
        await setNoOfficial(false)
        await setShare(false)
      }
      setTimeout(() => setLoading(false), 500)
    } catch (e) {
      let error = 'Please try again.'
      if (e?.info?.responseBody?.detail) {
        if (Array.isArray(e?.info?.responseBody?.detail)) {
          error = e.info.responseBody.detail.join('/n')
        } else {
          error = e?.info?.responseBody?.detail
        }
      }
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'We encountered an error loading the data.', type: 'error' }])
      toastRef.current.show()

      console.log('ERROR ACTION', e, error)
      setNoOfficial(true)
    }
  }

  const completeAction = async () => {
    try {
      const t = await globalState.api.actions.completeAction(item.id)
      console.log('COMPLETE ACTION', t)
      if (activePostId) {
        const result = await globalState.api.posts.getPost(activePostId)
        homeActions.setActivePost(result)
      }
      getCompletors()
    } catch (e) {
      Navigation.pop(componentId)
      console.log('COMPLETE CR', JSON.stringify(e))
    }
  }

  const handleNavigationProfile = async (isMain = false) => {
    if (isNavigating) {
      return
    }

    const body = isMain ? { updateAddress: true, reloadCB: getAction } : { externalUser: creator }
    console.log('CREATOR', isMain, JSON.stringify(body, null, 3))
    await setNavigating(true)
    await executeNavigation('Profile', componentId, body, true)
    await setNavigating(false)
  }

  const getErrorText = () => {
    if (share && action) {
      console.log('ACTION', JSON.stringify(action, null, 3))
      try {
        const displayTitle = action?.targeted_reps.office_info[0]?.display_title ? action?.targeted_reps?.office_info[0]?.display_title : ''
        return `This action wants people to contact their ${displayTitle}, but you aren't represented by this type of office.`
      } catch (e) {
        console.log('CONTACT REP TITLE', e)
        return "Unified doesn't know which representative you should contact for this action."
      }
    } else {
      return "Unified doesn't know which representative you should contact for this action."
    }
  }

  const getErrorPill = () => {
    if (share && action) {
      return <Pill onPress={() => publishAction()} icon={faClipboard} text="Include this action in your own post" />
    } else {
      return <Pill onPress={() => handleNavigationProfile(true)} icon={faUser} text="Update your current address" />
    }
  }

  const publishAction = async () => {
    await postActions.addAction(action)
    executeNavigationPostCreation('CreatePost', 'CreatePostHeader', { componentId })
  }

  const renderScene = ({ route }) => {
    switch (route.key) {
    case 'email':
      return <Email completeAction={completeAction} {...emailFormProps} />
    case 'call':
      return <Call completeAction={completeAction} {...callProps} />
    default:
      return null
    }
  }

  const layout = useWindowDimensions()

  const [routes] = React.useState([
    { key: 'email', title: 'Email' },
    { key: 'call', title: 'Call' },
  ])

  const renderTabBar = props => {
    const inputRange = props.navigationState.routes.map((x, i) => i)

    return (
      <View style={styles.tabBar}>
        <View style={{ backgroundColor: unifiedLightGray, height: 2, width: '100%', position: 'absolute', bottom: 0 }} />
        {props.navigationState.routes.map((route, i) => {
          const opacity = props.position.interpolate({
            inputRange,
            outputRange: inputRange.map(inputIndex => (inputIndex === i ? 1 : 0.5)),
          })
          let styleBox
          if (index === i) {
            styleBox = { ...styles.tabItem, borderBottomColor: '#CA09BC', borderBottomWidth: 2 }
          } else {
            styleBox = { ...styles.tabItem }
          }
          if (i === 0) {
            styleBox.marginLeft = 10
          }
          if (i === 1) {
            styleBox.marginLeft = 30
          }
          const getIcon = () => {
            if (route.title === 'Email') {
              if (index === i) {
                return envelopeSolid
              } else {
                return faEnvelope
              }
            } else if (route.title === 'Call') {
              if (index === i) {
                return phoneSolid
              } else {
                return faPhone
              }
            }
          }
          return (
            <TouchableOpacity key={`tab-${i}`} style={styleBox} onPress={() => setIndex(i)}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <FontAwesomeIcon icon={getIcon()} color={monochroma} size={25} />
                <Animated.Text style={[styles.tabText, { opacity }, { marginLeft: 8 }]} numberOfLines={1}>
                  {route.title}
                </Animated.Text>
              </View>
            </TouchableOpacity>
          )
        })}
      </View>
    )
  }

  const sendMail = () => {
    if (emailSent) {
      Navigation.pop(componentId)
      return
    }
    const sub = subject.trim()
    const body = emailText.trim()
    if (!email.trim()) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'Unfortunately, this representative does not seem to have a valid email address.', type: 'error' }])
      toastRef.current.show()
    }
    if (!sub || !body) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'Both subject and body are necesary.', type: 'error' }])
      toastRef.current.show()
    } else if (email.trim()) {
      setEmailSent(true)
      completeAction()
      sendEmail(email, subject, emailText)
    }
  }


  const call = () => {
    if (called) {
      Navigation.pop(componentId)
      return
    }
    if (!phoneNumber) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'Unfortunately, this representative does not seem to have a valid phone number.', type: 'error' }])
      toastRef.current.show()
      return
    }
    setCalled(true)
    completeAction()
    Linking.openURL(`tel:${phoneNumber}`)
  }

  return (
    <>
      <FormWrapper style={styles.contactRep}>
        {!item.is_reported && (
          <>
            <View
              style={{ marginLeft: 22, marginRight: 22, marginBottom: 22, marginTop: 14, flexDirection: 'row', backgroundColor: 'transparent', justifyContent: 'space-between', alignItems: 'center' }}>
              <TouchableOpacity onPress={() => handleNavigationProfile(false)} style={styles.namePicture}>
                <Image transition={false} style={styles.picture} source={picture ? { uri: picture.uri } : require('../../../assets/images/noUser.png')} />
                <Text style={styles.by}>
                  {displayName}
                  <Text style={[styles.date, { paddingLeft: 10, backgroundColor: 'transparent' }]}>
                    {'  '}
                    {getFormat(item?.start_date)}
                  </Text>{' '}
                </Text>
              </TouchableOpacity>
              {!ownPost && (
                <Pressable onPress={() => optionsRef?.current?.show()} style={{ backgroundColor: 'transparent', alignItems: 'flex-end', justifyContent: 'center', height: 42, width: 60 }}>
                  <FontAwesomeIcon icon={faEllipsisV} color={'#8D8D8D'} size={14} />
                </Pressable>
              )}
            </View>

            <Text selectable style={styles.description}>
              {item?.description}
            </Text>
            <View style={{ marginLeft: 22, marginRight: 22, marginTop: 10 }}>
              <SocialBlurp completors={completors} componentId={componentId} totalCompletors={completors.length} />
            </View>
            {loading && (
              <View style={{ flex: 1, backgroundColor: 'transparent', height: '100%', alignItems: 'center', justifyContent: 'center', paddingTop: 25 }}>
                <ActivityIndicator color={unifiedDarkGray} size="large" />
              </View>
            )}
            {noOfficial && !loading && (
              <>
                <View style={{ backgroundColor: '#FFE8F3', marginLeft: 22, marginRight: 22, marginTop: 22, padding: 13, borderRadius: 8 }}>
                  <Text style={{ color: 'black', fontSize: 14, fontWeight: medium }}>Uh oh...</Text>
                  <Text style={{ color: 'black', fontSize: 14 }}>{getErrorText()}</Text>
                </View>
                <View style={{ marginLeft: 22, marginRight: 22, marginTop: 22 }}>
                  <Text style={{ color: monochroma, fontSize: 14, fontWeight: medium, marginBottom: 7 }}>What you can do:</Text>
                  {getErrorPill()}
                </View>
              </>
            )}

            {!noOfficial && !loading && (
              <>
                <View style={styles.repHolder}>
                  {photo ? (
                    <Image transition={false} style={styles.repPicture} source={{ uri: photo }} />
                  ) : (
                    <Image transition={false} style={styles.repPicture} source={require('../../../assets/images/noUser.png')} />
                  )}
                  <View>
                    <Text numberOfLines={1} style={styles.repName}>
                      {name}
                    </Text>
                    <Text numberOfLines={1} style={styles.repTitle}>
                      {title}
                    </Text>
                    {party === 'Democratic Party' && <FontAwesomeIcon color={unifiedBlue} size={16} icon={faDemocrat} />}
                    {party === 'Republican Party' && <FontAwesomeIcon color={unifiedRed} size={16} icon={faRepublican} />}
                  </View>
                </View>
                <View style={{height:10}}/>
                <SimpleTabs setExternalTab={setExternalTab} componentId={componentId} completeAction={completeAction} emailFormProps={emailFormProps} callProps={callProps} />
                {Platform.OS === 'android' && (<View style={{height:100}}/>)}
              </>
            )}
          </>
        )}
        {item.is_reported && (
          <View style={{ marginLeft: 22, marginRight: 22, flexDirection: 'row', backgroundColor: 'transparent', justifyContent: 'space-between' }}>
            <View style={{ padding: 14, borderColor: '#F72585', borderWidth: 1, borderRadius: 8 }}>
              <Text style={{ fontSize: 14, color: monochroma }}>You reported this action previously, so it is no longer visible on your feed.</Text>
            </View>
          </View>
        )}
        <GenericOptions
          cb={() => {
            getAction()
            refreshParent()
            item.is_reported = true // hacky way so it looks faster, will be overwritten by the correct info in about 3s
          }}
          type="action"
          optionstRef={optionsRef}
          reportDialogRef={reportDialogRef}
          oid={item.id}
        />

      </FormWrapper>
      <View style={{position:'absolute', bottom:0, backgroundColor:monochroma2, width:'100%', paddingHorizontal:16}}>
        {externalTab === 0          ? (<TouchableOpacity onPress={() => {if (!confirmed) {
          showConfirmActionSheet('before contacting a representative.')
          return
        } else {sendMail()}}} style={emailSent ? styles.sentButton : styles.sendButton}>
          <Text style={styles.sendButtonText}>{emailSent ? 'Sent!' : 'Send'}</Text>
        </TouchableOpacity>)          :        (<TouchableOpacity onPress={() => {if (!confirmed) {
          showConfirmActionSheet('before contacting a representative.')
          return
        } else {call()}}} style={called ? styles.sentButton : styles.sendButton}>
          <Text style={styles.sendButtonText}>{called ? 'Called!' : 'Call'}</Text>
        </TouchableOpacity>)}
      </View>
    </>
  )
}

export default ContactRep
