/* eslint-disable react-native/no-inline-styles */
import React, { useContext, useEffect, useState } from 'react'
import StyleSheetFactory from './styles'
import { Alert, Linking, Text, TextInput, TouchableOpacity, useColorScheme, View } from 'react-native'
import { sendEmail } from '../../../Utils/sendEmail'
import { medium } from '../../../Utils/sizes'
import { faEnvelope } from '@fortawesome/pro-solid-svg-icons'
import Pill from '../../../Pill'
import { ToastContext } from '../../../Home/ToastStackWrapper'

const Email = ({ completeAction, email = '', subject, setSubject, emailText, setEmailText, emailSent, setEmailSent }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const monochroma = isDarkMode ? 'white' : 'black'

  useEffect(() => {
    console.log('MOUNT EMAIL')
  }, [])

  useEffect(() => {
    return () => {
      console.log('UNMOUNT EMAIL')
    }
  }, [])

  const { setToasts, toastRef } = useContext(ToastContext)

  const sendMail = () => {
    if (emailSent) {
      return
    }
    const sub = subject.trim()
    const body = emailText.trim()
    if (!email.trim()) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'Unfortunately, this representative does not seem to have a valid email address.', type: 'error' }])
      toastRef.current.show()
    }
    if (!sub || !body) {
      setToasts(prevToasts => [...prevToasts, { id: Date.now(), message: 'Both subject and body are necesary.', type: 'error' }])
      toastRef.current.show()
    } else {
      setEmailSent(true)
      completeAction()
      sendEmail(email, subject, emailText)
    }
  }

  return (
    <>
      {email.trim() === '' ? (
        <>
          <View style={{ backgroundColor: '#FFE8F3', marginLeft: 22, marginRight: 22, marginTop: 22, padding: 13, borderRadius: 8 }}>
            <Text style={{ color: 'black', fontSize: 14, fontWeight: medium }}>Unified does not have an email for this rep</Text>
            <Text style={{ color: 'black', fontSize: 14 }}>This means you won't be able to email them right now.</Text>
          </View>
          <View style={{ marginLeft: 22, marginRight: 22, marginTop: 22 }}>
            <Text style={{ color: monochroma, fontSize: 14, fontWeight: medium, marginBottom: 7 }}>What you can do:</Text>
            <Pill onPress={() => Linking.openURL('mailto:<EMAIL>')} icon={faEnvelope} text="Help us update our information" />
          </View>
        </>
      ) : (
        <View style={styles.emailContainer}>
          <Text style={styles.subject}>Email subject line:</Text>
          <TextInput style={styles.subjectText} onChangeText={setSubject} value={subject} />
          <Text style={styles.emailBody}>Email body:</Text>
          <View style={styles.emailBodyTextHolder}>
            <TextInput style={styles.emailBodyText} scrollEnabled={false} onChangeText={setEmailText} textAlignVertical="top" multiline value={emailText} />
          </View>
          <View style={{ height: 85 }} />
          {/* <TouchableOpacity onPress={() => sendMail()} style={emailSent ? styles.sentButton : styles.sendButton}>
            <Text style={styles.sendButtonText}>{emailSent ? 'Sent!' : 'Send'}</Text>
          </TouchableOpacity> */}
        </View>
      )}
    </>
  )
}

export default Email
