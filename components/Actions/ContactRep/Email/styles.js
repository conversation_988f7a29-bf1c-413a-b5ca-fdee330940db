import { StyleSheet } from 'react-native'
import { medium } from '../../../Utils/sizes'
import { darkModeGray, unifiedBlue, unifiedGreen } from '../../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      emailContainer: {
        flexGrow: 1,
        backgroundColor: 'transparent',
        marginLeft: 20,
        marginRight: 20,
      },
      subject: {
        fontWeight: medium,
        color: monochroma,
        fontSize: 14,
        marginTop: 30,
      },
      subjectText: {
        color: monochroma,
        fontSize: 16,
        marginTop: 10,
        paddingBottom: 7,
        borderBottomColor: '#BABABA',
        borderBottomWidth: 1,
      },
      emailBody: {
        fontWeight: medium,
        color: monochroma,
        fontSize: 14,
        marginTop: 22,
      },
      emailBodyTextHolder: {
        marginTop: 12,
        padding: 12,
        paddingTop: 10,
        borderColor: '#BABABA',
        borderWidth: 1,
        borderRadius: 3,
      },
      emailBodyText: {
        color: monochroma,
        // backgroundColor: 'orange',
        fontSize: 14,
        minHeight: 170,
      },
      sendButton: {
        backgroundColor: unifiedBlue,
        borderRadius: 8,
        marginTop: 24,
        height: 54,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 30,
      },
      sentButton: {
        backgroundColor: unifiedGreen,
        borderRadius: 8,
        marginTop: 24,
        height: 54,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 30,
      },
      sendButtonText: {
        fontWeight: medium,
        color: 'white',
        fontSize: 14,
      },
    })
  }
}
