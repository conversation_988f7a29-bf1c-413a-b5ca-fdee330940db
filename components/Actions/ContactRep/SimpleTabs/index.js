import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import Call from '../Call'
import Email from '../Email'
import StyleSheetFactory from './styles'
import React, { useRef, useState } from 'react'
import { View, Text, useColorScheme, Pressable, Dimensions, ScrollView } from 'react-native'
import { faEnvelope, faPhone } from '@fortawesome/pro-regular-svg-icons'
import { faEnvelope as envelopeSolid, faPhone as phoneSolid } from '@fortawesome/pro-solid-svg-icons'

const SimpleTabs = ({ componentId, completeAction, emailFormProps = {}, callProps = {}, setExternalTab = () => '' }) => {
  const isDarkMode = useColorScheme() === 'dark'
  const styles = StyleSheetFactory.getSheet(isDarkMode)
  const screenWidth = Dimensions.get('window').width
  const monochroma = isDarkMode ? 'white' : 'black'

  const tabsRef = useRef()
  const [currentPage, setCurrentPage] = useState(0)

  const handleScroll = event => {
    const offsetX = event.nativeEvent.contentOffset.x
    const pageIndex = Math.round(offsetX / screenWidth)
    setCurrentPage(pageIndex)
    setExternalTab(pageIndex)
  }

  const goToPage = pageNumber => {
    tabsRef.current.scrollTo({
      x: screenWidth * pageNumber,
      y: 0,
      animated: true,
    })
  }

  const getIcon = tabTitle => {
    if (tabTitle === 'Email') {
      if (currentPage === 0) {
        return envelopeSolid
      } else {
        return faEnvelope
      }
    } else if (tabTitle === 'Call') {
      if (currentPage === 1) {
        return phoneSolid
      } else {
        return faPhone
      }
    }
  }

  const Tab = ({ initialIndex, title }) => {
    return (
      <Pressable
        onPress={() => {
          goToPage(initialIndex)
        }}
        style={styles.tabButton}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <FontAwesomeIcon icon={getIcon(title)} color={monochroma} size={25} />
          <Text style={[styles.tabLabel(currentPage, initialIndex), { paddingLeft: 10 }]}>{title}</Text>
        </View>
        {currentPage === initialIndex && <View style={styles.activeTabIndicator} />}
      </Pressable>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.spacer} />
      <View style={styles.divider} />
      <View style={styles.tabsContainer}>
        <Tab initialIndex={0} title="Email" />
        <Tab initialIndex={1} title="Call" />
      </View>
      <ScrollView scrollEnabled={false} ref={tabsRef} scrollEventThrottle={16} onScroll={handleScroll} pagingEnabled={true} horizontal={true} style={styles.scrollContainer}>
        <View style={styles.feedContainer(screenWidth)}>
          <Email completeAction={completeAction} {...emailFormProps} />
        </View>
        <View style={styles.feedContainer(screenWidth)}>
          <Call completeAction={completeAction} {...callProps} />
        </View>
      </ScrollView>
    </View>
  )
}

export default SimpleTabs
