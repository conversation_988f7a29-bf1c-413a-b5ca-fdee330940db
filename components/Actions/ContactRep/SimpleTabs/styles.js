import { StyleSheet } from 'react-native'
import { medium } from '../../../Utils/sizes'
import { unifiedLightGray } from '../../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white' // Not used as of now.

    return StyleSheet.create({
      container: {
        flex: 1,
      },
      spacer: {
        height: 26,
        backgroundColor: 'transparent',
      },
      divider: {
        height: 2,
        width: '100%',
        backgroundColor: unifiedLightGray,
      },
      tabsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        position: 'absolute',
        backgroundColor: 'transparent',
        width: '100%',
        height: 28,
      },
      tabButton: {
        alignItems: 'center',
        justifyContent: 'space-between',
        width: 100,
      },
      tabLabel: (currentPage, pageIndex) => ({
        fontSize: 14,
        color: currentPage === pageIndex ? monochroma : unifiedLightGray,
        fontWeight: medium,
      }),
      activeTabIndicator: {
        height: 2,
        width: 100,
        backgroundColor: '#CA09BC',
      },
      scrollContainer: {
        flex: 1,
        backgroundColor: 'transparent',
        maxWidth: '100%',
        flexDirection: 'row',
      },
      feedContainer: screenWidth => ({
        width: screenWidth,
        backgroundColor: 'transparent',
      }),
    })
  }
}
