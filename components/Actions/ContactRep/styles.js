import { StatusBar, StyleSheet } from 'react-native'
import { medium } from '../../Utils/sizes'
import { darkModeGray, unifiedBlue, unifiedDarkGray, unifiedGreen, unifiedPurple, unifiedRoyalBlue } from '../../Utils/colors'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? unifiedRoyalBlue : 'white'

    return StyleSheet.create({
      //TABS
      tabText: {
        color: monochroma,
      },
      tabBar: {
        flexDirection: 'row',
        backgroundColor: monochroma2,
        paddingTop: StatusBar.currentHeight,
      },
      tabItem: {
        // flex: 1,
        alignItems: 'center',
        padding: 10,
        fontSize: 14,
      },
      //END TABS
      namePicture: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'transparent',
      },
      date: {
        fontSize: 11,
        color: unifiedDarkGray,
      },
      by: {
        fontSize: 16,
        fontWeight: medium,
        color: monochroma,
      },
      picture: {
        width: 42,
        height: 42,
        marginRight: 10,
        borderRadius: 999,
      },
      contactRep: {
        paddingTop: 0,
        backgroundColor: monochroma2,
      },
      description: {
        marginLeft: 22,
        marginRight: 22,
        fontSize: 14,
        color: monochroma,
      },
      repHolder: {
        marginLeft: 22,
        marginRight: 22,
        marginTop: 32,
        // backgroundColor: isDarkMode ? '#3D3D3D' : '#FAFAFA',
        borderRadius: 20,
        paddingLeft: 12,
        paddingRight: 12,
        paddingTop: 16,
        paddingBottom: 16,
        flexDirection: 'row',
      },
      repPicture: {
        width: 80,
        height: 80,
        marginRight: 12,
        borderRadius: 200,
      },
      repDescription: {
        justifyContent: 'center',
      },
      repName: {
        fontSize: 16,
        fontWeight: medium,
        color: monochroma,
        marginBottom: 4,
      },
      repTitle: {
        fontSize: 14,
        color: monochroma,
        marginBottom: 6,
      },
      chooseText: {
        marginLeft: 22,
        marginRight: 22,
        fontSize: 14,
        marginTop: 30,
        fontWeight: medium,
        color: monochroma,
      },
      emailPhoneContainer: {
        marginLeft: 0,
        marginRight: 0,
        marginTop: 28,
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        backgroundColor: 'transparent',
      },
      emailContainer: {
        alignItems: 'center',
      },
      phoneContainer: {
        alignItems: 'center',
      },
      emailPhoneText: {
        fontSize: 14,
        marginTop: 4,
        color: monochroma,
      },
      emailPhoneTextBold: {
        fontSize: 14,
        marginTop: 4,
        fontWeight: medium,
        color: monochroma,
      },
      sendButton: {
        backgroundColor: unifiedPurple,
        borderRadius: 8,
        marginTop: 24,
        height: 54,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 30,
      },
      sentButton: {
        backgroundColor: unifiedGreen,
        borderRadius: 8,
        marginTop: 24,
        height: 54,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 30,
      },
      sendButtonText: {
        fontWeight: medium,
        color: 'white',
        fontSize: 14,
      },
    })
  }
}
