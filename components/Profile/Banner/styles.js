import { Platform, StyleSheet } from 'react-native'
import { medium } from '../../Utils/sizes'

export default class StyleSheetFactory {
  static getSheet(isDarkMode) {
    const monochroma = isDarkMode ? 'white' : 'black'
    const monochroma2 = isDarkMode ? 'black' : 'white'

    return StyleSheet.create({
      banner: {
        backgroundColor: 'transparent',
        width: '100%',
        overflow: 'visible',
      },
      bannerImageHolder: {
        position: 'absolute',
        top: 0,
        width: '100%',
        alignItems: 'center',
      },
      bannerImage: {
        position: 'absolute',
        alignSelf: 'center',
      },
      indicatorHolder: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5),',
        justifyContent: 'center',
        alignItems: 'center',
      },
      navigator: {
        position: 'absolute',
        left: 22,
        top: Platform.select({ ios: 55, android: 25 }),
        backgroundColor: 'rgba(255,255,255,0.5)',
        padding: 7,
        borderRadius: 99,
        width: 28,
        height: 28,
        justifyContent: 'center',
        alignItems: 'center',
      },
      rightButton: {
        position: 'absolute',
        right: 22,
        top: Platform.select({ ios: 45, android: 25 }),
        backgroundColor: 'rgba(255,255,255,0.5)',
        padding: 7,
        borderRadius: 99,
      },
      rightButtonContainer: {
        position: 'absolute',
        right: 22,
        top: Platform.select({ ios: 55, android: 25 }),
        flexDirection: 'row',
      },
      headerButton: {
        backgroundColor: '#E8E8E880',
        padding: 7,
        borderRadius: 99,
        marginLeft: 12,
        width: 28,
        height: 28,
        justifyContent: 'center',
        alignItems: 'center',
      },
      saveButton: {
        right: 22,
        padding: 7,
        borderRadius: 99,
        alignItems: 'center',
        position: 'absolute',
        justifyContent: 'center',
        backgroundColor: 'rgba(255,255,255,0.5)',
        top: Platform.select({ ios: 45, android: 25 }),
      },
      saveButtonText: {
        fontSize: 17,
        color: 'black',
        fontWeight: medium,
      },
      cancelButton: {
        position: 'absolute',
        left: 22,
        top: Platform.select({ ios: 45, android: 25 }),
        backgroundColor: 'rgba(255,255,255,0.5)',
        padding: 7,
        borderRadius: 99,
      },
      overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: 260, 
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
      },
    })
  }
}
