import React, { useEffect, useState } from 'react'
import { Image, View, Platform } from 'react-native'
// import * as FS from '@dr.pogodin/react-native-fs'
import FastImage from 'react-native-fast-image'

// Utility function to generate the local path for the image based on the URL
// const getImagePath = imageUrl => {
//   const imageName = imageUrl.split('/').pop()
//   return `${FS.DocumentDirectoryPath}/${imageName}`
// }

// CachedImage component
const CachedImage = props => {
  const { source, style, ...restProps } = props
  const [localImagePath, setLocalImagePath] = useState(null)

  // useEffect(() => {
  //   const downloadAndCacheImage = async () => {
  //     let imagePath = null
  //     try {
  //       const imageUrl = source.uri
  //       imagePath = getImagePath(imageUrl)

  //       // Check if the image already exists
  //       const fileExists = await FS.exists(imagePath)

  //       if (fileExists) {
  //         setLocalImagePath(imagePath) // Use the cached image
  //       } else {
  //         // Download and cache the image
  //         const downloadOptions = {
  //           fromUrl: imageUrl,
  //           toFile: imagePath,
  //           begin: res => {
  //             // console.log('Download has begun:', res)
  //           },
  //           progress: res => {
  //             const percentage = (res.bytesWritten / res.contentLength) * 100
  //             // console.log(`Download progress: ${percentage}%`)
  //           },
  //         }

  //         const result = await FS.downloadFile(downloadOptions).promise

  //         if (result.statusCode === 200) {
  //           setLocalImagePath(imagePath) // Use the cached image
  //         } else {
  //           throw new Error('Download failed with status: ' + result.statusCode)
  //         }
  //       }
  //     } catch (error) {
  //       console.error('Error downloading or caching the image:', error)
  //       if (imagePath) {
  //         await FS.unlink(imagePath).catch(unlinkError => console.error('Error removing incomplete file:', unlinkError))
  //       }
  //     }
  //   }

  //   downloadAndCacheImage()
  // }, [source])

  const displaySource = localImagePath && !localImagePath.endsWith('.gif') ? { uri: `file://${localImagePath}` } : source

  // return Platform.OS === 'android' ? (
  //   <FastImage
  //     source={{
  //       uri: source.uri,
  //       priority: FastImage.priority.high,
  //       cache: FastImage.cacheControl.immutable, // Aggressive caching
  //     }}
  //     style={style}
  //     {...restProps}
  //   />
  // ) : (
  //   <Image source={displaySource} style={style} {...restProps} />
  // )

  if (Platform.OS === 'android') {
    return (
      <FastImage
        source={{
          uri: source.uri,
          priority: FastImage.priority.high,
          cache: FastImage.cacheControl.immutable, // Aggressive caching
        }}
        style={style}
        {...restProps}
      />
    )
  } else {
    if (source?.uri?.endsWith('.gif')) {
      return <Image source={displaySource} style={style} {...restProps} />
    } else {
      return (
        <FastImage
          source={{
            uri: source.uri,
            priority: FastImage.priority.high,
            cache: FastImage.cacheControl.immutable, // Aggressive caching
          }}
          style={style}
          {...restProps}
        />
      )
    }
  }
}

export default CachedImage
